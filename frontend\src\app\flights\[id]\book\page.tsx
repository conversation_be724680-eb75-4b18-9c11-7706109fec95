'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useSearchParams, useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/authStore';
import { flightAPI, bookingAPI } from '@/lib/api';
import { Flight } from '@/store/flightStore';
import { formatCurrency } from '@/lib/utils';
import Navbar from '@/components/layout/Navbar';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { Plane, User, Mail, Phone, Calendar, CreditCard } from 'lucide-react';

const BookFlightPage = () => {
  const params = useParams();
  const searchParams = useSearchParams();
  const router = useRouter();
  const { isAuthenticated, user } = useAuthStore();
  
  const [flight, setFlight] = useState<Flight | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isBooking, setIsBooking] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const flightClass = searchParams.get('class') || 'economy';
  const [formData, setFormData] = useState({
    passenger_name: user?.name || '',
    passenger_email: user?.email || '',
    passenger_phone: '',
    passenger_dob: '',
    passenger_passport: '',
    passengers_count: 1,
  });

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }

    const fetchFlight = async () => {
      try {
        const response = await flightAPI.getById(Number(params.id));
        setFlight(response.data || response);
      } catch (error) {
        console.error('Error fetching flight:', error);
        router.push('/flights/search');
      } finally {
        setIsLoading(false);
      }
    };

    fetchFlight();
  }, [params.id, isAuthenticated, router]);

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleBooking = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!flight) return;

    setIsBooking(true);
    setErrors({});

    try {
      const bookingData = {
        flight_id: flight.id,
        ...formData,
        class: flightClass,
      };

      const response = await bookingAPI.create(bookingData);
      router.push(`/bookings/${response.data.id}`);
    } catch (error: any) {
      console.error('Booking error:', error);
      if (error.response?.data?.errors) {
        setErrors(error.response.data.errors);
      } else {
        setErrors({ general: error.response?.data?.message || 'Booking failed. Please try again.' });
      }
    } finally {
      setIsBooking(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-secondary-50">
        <Navbar />
        <div className="flex items-center justify-center py-20">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-secondary-600">Loading flight details...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!flight) {
    return (
      <div className="min-h-screen bg-secondary-50">
        <Navbar />
        <div className="flex items-center justify-center py-20">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-secondary-900 mb-2">Flight not found</h2>
            <Button onClick={() => router.push('/flights/search')}>
              Back to Search
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const price = flight.prices[flightClass as keyof typeof flight.prices] || flight.prices.economy;
  const totalPrice = price * formData.passengers_count;

  return (
    <div className="min-h-screen bg-secondary-50">
      <Navbar />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-secondary-900 mb-2">Complete Your Booking</h1>
          <p className="text-secondary-600">Please fill in the passenger details below</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Booking Form */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h2 className="text-xl font-semibold text-secondary-900 mb-6">Passenger Information</h2>
              
              <form onSubmit={handleBooking} className="space-y-6">
                {errors.general && (
                  <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg">
                    {errors.general}
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Full Name"
                    placeholder="Enter passenger name"
                    value={formData.passenger_name}
                    onChange={(e) => handleInputChange('passenger_name', e.target.value)}
                    error={errors.passenger_name}
                    icon={<User />}
                    required
                  />

                  <Input
                    label="Email Address"
                    type="email"
                    placeholder="Enter email address"
                    value={formData.passenger_email}
                    onChange={(e) => handleInputChange('passenger_email', e.target.value)}
                    error={errors.passenger_email}
                    icon={<Mail />}
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Phone Number"
                    type="tel"
                    placeholder="Enter phone number"
                    value={formData.passenger_phone}
                    onChange={(e) => handleInputChange('passenger_phone', e.target.value)}
                    error={errors.passenger_phone}
                    icon={<Phone />}
                    required
                  />

                  <Input
                    label="Date of Birth"
                    type="date"
                    value={formData.passenger_dob}
                    onChange={(e) => handleInputChange('passenger_dob', e.target.value)}
                    error={errors.passenger_dob}
                    icon={<Calendar />}
                    required
                  />
                </div>

                <Input
                  label="Passport Number (Optional)"
                  placeholder="Enter passport number"
                  value={formData.passenger_passport}
                  onChange={(e) => handleInputChange('passenger_passport', e.target.value)}
                  error={errors.passenger_passport}
                  icon={<CreditCard />}
                />

                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-1">
                    Number of Passengers
                  </label>
                  <select
                    value={formData.passengers_count}
                    onChange={(e) => handleInputChange('passengers_count', parseInt(e.target.value))}
                    className="block w-full border border-secondary-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  >
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9].map(num => (
                      <option key={num} value={num}>
                        {num} {num === 1 ? 'Passenger' : 'Passengers'}
                      </option>
                    ))}
                  </select>
                </div>

                <Button
                  type="submit"
                  size="lg"
                  className="w-full"
                  isLoading={isBooking}
                >
                  Confirm Booking - {formatCurrency(totalPrice)}
                </Button>
              </form>
            </div>
          </div>

          {/* Flight Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-sm p-6 sticky top-6">
              <h3 className="text-lg font-semibold text-secondary-900 mb-4">Flight Summary</h3>
              
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  {flight.airline_logo_url && (
                    <img
                      src={flight.airline_logo_url}
                      alt={flight.airline_name}
                      className="h-8 w-8 object-contain"
                    />
                  )}
                  <div>
                    <p className="font-medium text-secondary-900">
                      {flight.airline_name} {flight.flight_number}
                    </p>
                    <p className="text-sm text-secondary-600">{flight.aircraft_type}</p>
                  </div>
                </div>

                <div className="border-t border-secondary-200 pt-4">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-secondary-600">Departure</span>
                    <span className="font-medium">{flight.departure.formatted_time}</span>
                  </div>
                  <p className="text-sm text-secondary-500">
                    {flight.departure.city} ({flight.departure.airport})
                  </p>
                </div>

                <div className="border-t border-secondary-200 pt-4">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-secondary-600">Arrival</span>
                    <span className="font-medium">{flight.arrival.formatted_time}</span>
                  </div>
                  <p className="text-sm text-secondary-500">
                    {flight.arrival.city} ({flight.arrival.airport})
                  </p>
                </div>

                <div className="border-t border-secondary-200 pt-4">
                  <div className="flex justify-between items-center">
                    <span className="text-secondary-600">Duration</span>
                    <span className="font-medium">{flight.duration_formatted}</span>
                  </div>
                </div>

                <div className="border-t border-secondary-200 pt-4">
                  <div className="flex justify-between items-center">
                    <span className="text-secondary-600">Class</span>
                    <span className="font-medium capitalize">{flightClass}</span>
                  </div>
                </div>

                <div className="border-t border-secondary-200 pt-4">
                  <div className="flex justify-between items-center text-lg font-semibold">
                    <span>Total Price</span>
                    <span className="text-primary-600">{formatCurrency(totalPrice)}</span>
                  </div>
                  <p className="text-sm text-secondary-500 mt-1">
                    {formatCurrency(price)} × {formData.passengers_count} passenger{formData.passengers_count > 1 ? 's' : ''}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookFlightPage;
