'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/authStore';
import { bookingAPI } from '@/lib/api';
import { formatCurrency, formatDate } from '@/lib/utils';
import Navbar from '@/components/layout/Navbar';
import Button from '@/components/ui/Button';
import { Plane, Calendar, MapPin, User, Phone, Mail } from 'lucide-react';

interface Booking {
  id: number;
  booking_reference: string;
  passenger_name: string;
  passenger_email: string;
  passenger_phone: string;
  passenger_dob: string;
  class: string;
  passengers_count: number;
  total_price: number;
  status: string;
  booking_date_formatted: string;
  flight: {
    id: number;
    airline_name: string;
    flight_number: string;
    departure: {
      airport: string;
      city: string;
      time: string;
      formatted_time: string;
      formatted_date: string;
    };
    arrival: {
      airport: string;
      city: string;
      time: string;
      formatted_time: string;
      formatted_date: string;
    };
    duration_formatted: string;
  };
}

const BookingsPage = () => {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated } = useAuthStore();
  const router = useRouter();

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }

    const fetchBookings = async () => {
      try {
        const response = await bookingAPI.getAll();
        setBookings(response.data || response);
      } catch (error: any) {
        console.error('Error fetching bookings:', error);
        setError('Failed to load bookings');
      } finally {
        setIsLoading(false);
      }
    };

    fetchBookings();
  }, [isAuthenticated, router]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-secondary-50">
        <Navbar />
        <div className="flex items-center justify-center py-20">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-secondary-600">Loading your bookings...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-secondary-50">
        <Navbar />
        <div className="flex items-center justify-center py-20">
          <div className="text-center">
            <div className="bg-red-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Plane className="h-8 w-8 text-red-600" />
            </div>
            <h2 className="text-2xl font-bold text-secondary-900 mb-2">Error Loading Bookings</h2>
            <p className="text-secondary-600 mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-secondary-50">
      <Navbar />
      
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-secondary-900 mb-2">My Bookings</h1>
          <p className="text-secondary-600">Manage and view your flight bookings</p>
        </div>

        {bookings.length === 0 ? (
          <div className="bg-white rounded-xl shadow-sm p-12 text-center">
            <div className="bg-secondary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Plane className="h-8 w-8 text-secondary-600" />
            </div>
            <h3 className="text-xl font-semibold text-secondary-900 mb-2">
              No bookings yet
            </h3>
            <p className="text-secondary-600 mb-6">
              Start planning your next adventure by searching for flights
            </p>
            <Button onClick={() => router.push('/')}>
              Search Flights
            </Button>
          </div>
        ) : (
          <div className="space-y-6">
            {bookings.map((booking) => (
              <div key={booking.id} className="bg-white rounded-xl shadow-sm border border-secondary-200 overflow-hidden">
                <div className="p-6">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                    {/* Booking Info */}
                    <div className="flex-1">
                      <div className="flex items-center gap-4 mb-4">
                        <div className="bg-primary-100 p-2 rounded-lg">
                          <Plane className="h-6 w-6 text-primary-600" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-secondary-900">
                            {booking.flight.airline_name} {booking.flight.flight_number}
                          </h3>
                          <p className="text-sm text-secondary-600">
                            Booking Reference: {booking.booking_reference}
                          </p>
                        </div>
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                          {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                        </span>
                      </div>

                      {/* Flight Route */}
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div>
                          <div className="flex items-center gap-2 text-secondary-600 mb-1">
                            <MapPin className="h-4 w-4" />
                            <span className="text-sm">Departure</span>
                          </div>
                          <p className="font-semibold text-secondary-900">
                            {booking.flight.departure.formatted_time}
                          </p>
                          <p className="text-sm text-secondary-600">
                            {booking.flight.departure.city} ({booking.flight.departure.airport})
                          </p>
                          <p className="text-xs text-secondary-500">
                            {booking.flight.departure.formatted_date}
                          </p>
                        </div>

                        <div className="text-center">
                          <div className="flex items-center justify-center gap-2 text-secondary-500 mb-1">
                            <div className="h-px bg-secondary-300 flex-1"></div>
                            <Plane className="h-4 w-4" />
                            <div className="h-px bg-secondary-300 flex-1"></div>
                          </div>
                          <p className="text-sm text-secondary-600">{booking.flight.duration_formatted}</p>
                        </div>

                        <div className="text-right">
                          <div className="flex items-center gap-2 text-secondary-600 mb-1 justify-end">
                            <MapPin className="h-4 w-4" />
                            <span className="text-sm">Arrival</span>
                          </div>
                          <p className="font-semibold text-secondary-900">
                            {booking.flight.arrival.formatted_time}
                          </p>
                          <p className="text-sm text-secondary-600">
                            {booking.flight.arrival.city} ({booking.flight.arrival.airport})
                          </p>
                          <p className="text-xs text-secondary-500">
                            {booking.flight.arrival.formatted_date}
                          </p>
                        </div>
                      </div>

                      {/* Passenger Details */}
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-secondary-500" />
                          <span className="text-secondary-600">Passenger:</span>
                          <span className="font-medium">{booking.passenger_name}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-secondary-500" />
                          <span className="text-secondary-600">Email:</span>
                          <span className="font-medium">{booking.passenger_email}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-secondary-500" />
                          <span className="text-secondary-600">Phone:</span>
                          <span className="font-medium">{booking.passenger_phone}</span>
                        </div>
                      </div>
                    </div>

                    {/* Price and Actions */}
                    <div className="lg:text-right">
                      <div className="mb-4">
                        <p className="text-2xl font-bold text-primary-600">
                          {formatCurrency(booking.total_price)}
                        </p>
                        <p className="text-sm text-secondary-600">
                          {booking.passengers_count} passenger{booking.passengers_count > 1 ? 's' : ''}
                        </p>
                        <p className="text-xs text-secondary-500 capitalize">
                          {booking.class} class
                        </p>
                      </div>

                      <div className="flex flex-col gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/bookings/${booking.id}`)}
                        >
                          View Details
                        </Button>
                        {booking.status === 'confirmed' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-red-600 hover:text-red-700"
                          >
                            Cancel Booking
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-secondary-50 px-6 py-3 border-t border-secondary-200">
                  <div className="flex items-center gap-2 text-sm text-secondary-600">
                    <Calendar className="h-4 w-4" />
                    <span>Booked on {booking.booking_date_formatted}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default BookingsPage;
