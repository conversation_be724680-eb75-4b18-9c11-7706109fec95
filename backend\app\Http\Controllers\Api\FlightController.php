<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\FlightResource;
use App\Models\Flight;
use Illuminate\Http\Request;
use Carbon\Carbon;

class FlightController extends Controller
{
    public function search(Request $request)
    {
        $request->validate([
            'from' => 'required|string',
            'to' => 'required|string',
            'departure_date' => 'required|date',
            'return_date' => 'nullable|date|after:departure_date',
            'passengers' => 'integer|min:1|max:9',
            'class' => 'in:economy,business,first',
        ]);

        $query = Flight::query();

        // Filter by departure and arrival cities
        $query->where('departure_city', 'like', '%' . $request->from . '%')
              ->where('arrival_city', 'like', '%' . $request->to . '%');

        // Filter by departure date
        $departureDate = Carbon::parse($request->departure_date);
        $query->whereDate('departure_time', $departureDate);

        // Filter by available seats for the requested class
        $class = $request->class ?? 'economy';
        $passengers = $request->passengers ?? 1;

        $seatColumn = 'available_seats_' . $class;
        $query->where($seatColumn, '>=', $passengers);

        // Order by departure time
        $query->orderBy('departure_time');

        $flights = $query->get();

        return FlightResource::collection($flights);
    }

    public function show(Flight $flight)
    {
        return new FlightResource($flight);
    }

    public function index()
    {
        $flights = Flight::orderBy('departure_time')->paginate(20);
        return FlightResource::collection($flights);
    }
}
