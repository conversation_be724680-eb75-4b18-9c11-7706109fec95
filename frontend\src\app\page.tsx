import Navbar from '@/components/layout/Navbar';
import FlightSearchForm from '@/components/flights/FlightSearchForm';
import { Plane, Shield, Clock, Star } from 'lucide-react';

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50">
      <Navbar />

      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-6xl font-bold text-secondary-900 mb-6">
              Find Your Perfect
              <span className="text-primary-600 block">Flight Journey</span>
            </h1>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              Discover the world with our comprehensive flight booking platform.
              Compare prices, find the best deals, and book your next adventure with confidence.
            </p>
          </div>

          {/* Search Form */}
          <div className="max-w-4xl mx-auto">
            <FlightSearchForm />
          </div>
        </div>

        {/* Background decoration */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
          <div className="absolute top-20 right-10 opacity-10">
            <Plane className="h-32 w-32 text-primary-600 transform rotate-45" />
          </div>
          <div className="absolute bottom-20 left-10 opacity-10">
            <Plane className="h-24 w-24 text-primary-600 transform -rotate-12" />
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-secondary-900 mb-4">
              Why Choose FlightBooker?
            </h2>
            <p className="text-lg text-secondary-600 max-w-2xl mx-auto">
              We make flight booking simple, secure, and affordable for travelers worldwide.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center p-6">
              <div className="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="h-8 w-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold text-secondary-900 mb-2">Best Prices</h3>
              <p className="text-secondary-600">
                Compare prices from multiple airlines to find the best deals for your journey.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="h-8 w-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold text-secondary-900 mb-2">Secure Booking</h3>
              <p className="text-secondary-600">
                Your personal and payment information is protected with industry-standard security.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="h-8 w-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold text-secondary-900 mb-2">24/7 Support</h3>
              <p className="text-secondary-600">
                Our customer support team is available around the clock to assist you.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Plane className="h-8 w-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold text-secondary-900 mb-2">Easy Booking</h3>
              <p className="text-secondary-600">
                Simple and intuitive booking process that gets you from search to confirmation quickly.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to Start Your Journey?
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            Join thousands of satisfied travelers who have booked their flights with us.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors">
              Search Flights Now
            </button>
            <button className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors">
              Learn More
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
