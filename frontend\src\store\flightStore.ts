import { create } from 'zustand';

export interface Flight {
  id: number;
  airline_name: string;
  airline_code: string;
  flight_number: string;
  aircraft_type?: string;
  departure: {
    airport: string;
    city: string;
    country: string;
    time: string;
    formatted_time: string;
    formatted_date: string;
  };
  arrival: {
    airport: string;
    city: string;
    country: string;
    time: string;
    formatted_time: string;
    formatted_date: string;
  };
  duration_minutes: number;
  duration_formatted: string;
  stops: number;
  stop_airports?: string[];
  prices: {
    economy: number;
    business?: number;
    first?: number;
  };
  available_seats: {
    economy: number;
    business: number;
    first: number;
  };
  airline_logo_url?: string;
  amenities?: string[];
  baggage_policy?: string;
  cancellation_policy?: string;
  is_refundable: boolean;
}

export interface SearchParams {
  from: string;
  to: string;
  departure_date: string;
  return_date?: string;
  passengers: number;
  class: 'economy' | 'business' | 'first';
  trip_type: 'one-way' | 'round-trip';
}

interface FlightState {
  flights: Flight[];
  selectedFlight: Flight | null;
  searchParams: SearchParams;
  isLoading: boolean;
  error: string | null;
  setFlights: (flights: Flight[]) => void;
  setSelectedFlight: (flight: Flight | null) => void;
  setSearchParams: (params: Partial<SearchParams>) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearFlights: () => void;
}

export const useFlightStore = create<FlightState>((set) => ({
  flights: [],
  selectedFlight: null,
  searchParams: {
    from: '',
    to: '',
    departure_date: '',
    return_date: '',
    passengers: 1,
    class: 'economy',
    trip_type: 'one-way',
  },
  isLoading: false,
  error: null,
  setFlights: (flights) => set({ flights }),
  setSelectedFlight: (flight) => set({ selectedFlight: flight }),
  setSearchParams: (params) =>
    set((state) => ({
      searchParams: { ...state.searchParams, ...params },
    })),
  setLoading: (loading) => set({ isLoading: loading }),
  setError: (error) => set({ error }),
  clearFlights: () => set({ flights: [], selectedFlight: null }),
}));
