<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Flight extends Model
{
    use HasFactory;

    protected $fillable = [
        'airline_name',
        'airline_code',
        'flight_number',
        'aircraft_type',
        'departure_airport',
        'departure_city',
        'departure_country',
        'arrival_airport',
        'arrival_city',
        'arrival_country',
        'departure_time',
        'arrival_time',
        'duration_minutes',
        'stops',
        'stop_airports',
        'price_economy',
        'price_business',
        'price_first',
        'available_seats_economy',
        'available_seats_business',
        'available_seats_first',
        'airline_logo_url',
        'amenities',
        'baggage_policy',
        'cancellation_policy',
        'is_refundable',
    ];

    protected $casts = [
        'departure_time' => 'datetime',
        'arrival_time' => 'datetime',
        'stop_airports' => 'array',
        'amenities' => 'array',
        'is_refundable' => 'boolean',
        'price_economy' => 'decimal:2',
        'price_business' => 'decimal:2',
        'price_first' => 'decimal:2',
    ];

    /**
     * Get the bookings for the flight.
     */
    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * Get the price for a specific class.
     */
    public function getPriceForClass($class)
    {
        return match($class) {
            'economy' => $this->price_economy,
            'business' => $this->price_business,
            'first' => $this->price_first,
            default => $this->price_economy,
        };
    }

    /**
     * Get available seats for a specific class.
     */
    public function getAvailableSeatsForClass($class)
    {
        return match($class) {
            'economy' => $this->available_seats_economy,
            'business' => $this->available_seats_business,
            'first' => $this->available_seats_first,
            default => $this->available_seats_economy,
        };
    }
}
