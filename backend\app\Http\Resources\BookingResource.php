<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BookingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'booking_reference' => $this->booking_reference,
            'passenger_name' => $this->passenger_name,
            'passenger_email' => $this->passenger_email,
            'passenger_phone' => $this->passenger_phone,
            'passenger_dob' => $this->passenger_dob->format('Y-m-d'),
            'passenger_passport' => $this->passenger_passport,
            'class' => $this->class,
            'passengers_count' => $this->passengers_count,
            'total_price' => $this->total_price,
            'status' => $this->status,
            'passenger_details' => $this->passenger_details,
            'booking_date' => $this->booking_date->format('Y-m-d H:i:s'),
            'booking_date_formatted' => $this->booking_date->format('M d, Y'),
            'flight' => new FlightResource($this->whenLoaded('flight')),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
        ];
    }
}
