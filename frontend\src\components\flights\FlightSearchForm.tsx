'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useFlightStore } from '@/store/flightStore';
import { flightAPI } from '@/lib/api';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { MapPin, Calendar, Users, Plane } from 'lucide-react';

const FlightSearchForm = () => {
  const router = useRouter();
  const { searchParams, setSearchParams, setFlights, setLoading, setError } = useFlightStore();
  const [formData, setFormData] = useState(searchParams);

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.from || !formData.to || !formData.departure_date) {
      setError('Please fill in all required fields');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const searchData = {
        from: formData.from,
        to: formData.to,
        departure_date: formData.departure_date,
        return_date: formData.trip_type === 'round-trip' ? formData.return_date : undefined,
        passengers: formData.passengers,
        class: formData.class,
      };

      const response = await flightAPI.search(searchData);
      setFlights(response.data || response);
      setSearchParams(formData);
      router.push('/flights/search');
    } catch (error) {
      console.error('Search error:', error);
      setError('Failed to search flights. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const today = new Date().toISOString().split('T')[0];
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  const tomorrowStr = tomorrow.toISOString().split('T')[0];

  return (
    <div className="bg-white rounded-2xl shadow-xl p-6 md:p-8">
      <form onSubmit={handleSearch} className="space-y-6">
        {/* Trip Type */}
        <div className="flex space-x-4">
          <label className="flex items-center">
            <input
              type="radio"
              name="trip_type"
              value="one-way"
              checked={formData.trip_type === 'one-way'}
              onChange={(e) => handleInputChange('trip_type', e.target.value)}
              className="mr-2 text-primary-600 focus:ring-primary-500"
            />
            <span className="text-secondary-700">One Way</span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="trip_type"
              value="round-trip"
              checked={formData.trip_type === 'round-trip'}
              onChange={(e) => handleInputChange('trip_type', e.target.value)}
              className="mr-2 text-primary-600 focus:ring-primary-500"
            />
            <span className="text-secondary-700">Round Trip</span>
          </label>
        </div>

        {/* From and To */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="From"
            placeholder="Departure city"
            value={formData.from}
            onChange={(e) => handleInputChange('from', e.target.value)}
            icon={<MapPin />}
            required
          />
          <Input
            label="To"
            placeholder="Destination city"
            value={formData.to}
            onChange={(e) => handleInputChange('to', e.target.value)}
            icon={<MapPin />}
            required
          />
        </div>

        {/* Dates */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="Departure Date"
            type="date"
            value={formData.departure_date}
            onChange={(e) => handleInputChange('departure_date', e.target.value)}
            min={today}
            icon={<Calendar />}
            required
          />
          {formData.trip_type === 'round-trip' && (
            <Input
              label="Return Date"
              type="date"
              value={formData.return_date || ''}
              onChange={(e) => handleInputChange('return_date', e.target.value)}
              min={formData.departure_date || tomorrowStr}
              icon={<Calendar />}
            />
          )}
        </div>

        {/* Passengers and Class */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-1">
              Passengers
            </label>
            <div className="relative">
              <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-secondary-400" />
              <select
                value={formData.passengers}
                onChange={(e) => handleInputChange('passengers', parseInt(e.target.value))}
                className="block w-full pl-10 pr-3 py-2 border border-secondary-300 rounded-lg bg-white text-secondary-900 focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500"
              >
                {[1, 2, 3, 4, 5, 6, 7, 8, 9].map(num => (
                  <option key={num} value={num}>
                    {num} {num === 1 ? 'Passenger' : 'Passengers'}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-secondary-700 mb-1">
              Class
            </label>
            <div className="relative">
              <Plane className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-secondary-400" />
              <select
                value={formData.class}
                onChange={(e) => handleInputChange('class', e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-secondary-300 rounded-lg bg-white text-secondary-900 focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500"
              >
                <option value="economy">Economy</option>
                <option value="business">Business</option>
                <option value="first">First Class</option>
              </select>
            </div>
          </div>
        </div>

        {/* Search Button */}
        <Button
          type="submit"
          size="lg"
          className="w-full"
          isLoading={useFlightStore.getState().isLoading}
        >
          Search Flights
        </Button>
      </form>
    </div>
  );
};

export default FlightSearchForm;
