import axios from 'axios';
import { useAuthStore } from '@/store/authStore';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = useAuthStore.getState().token;
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      useAuthStore.getState().logout();
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  register: async (data: { name: string; email: string; password: string; password_confirmation: string }) => {
    const response = await api.post('/register', data);
    return response.data;
  },
  
  login: async (data: { email: string; password: string }) => {
    const response = await api.post('/login', data);
    return response.data;
  },
  
  logout: async () => {
    const response = await api.post('/logout');
    return response.data;
  },
  
  getUser: async () => {
    const response = await api.get('/user');
    return response.data;
  },
};

// Flight API
export const flightAPI = {
  search: async (params: {
    from: string;
    to: string;
    departure_date: string;
    return_date?: string;
    passengers?: number;
    class?: string;
  }) => {
    const response = await api.get('/flights/search', { params });
    return response.data;
  },
  
  getAll: async () => {
    const response = await api.get('/flights');
    return response.data;
  },
  
  getById: async (id: number) => {
    const response = await api.get(`/flights/${id}`);
    return response.data;
  },
};

// Booking API
export const bookingAPI = {
  create: async (data: {
    flight_id: number;
    passenger_name: string;
    passenger_email: string;
    passenger_phone: string;
    passenger_dob: string;
    passenger_passport?: string;
    class: string;
    passengers_count?: number;
    passenger_details?: any;
  }) => {
    const response = await api.post('/bookings', data);
    return response.data;
  },
  
  getAll: async () => {
    const response = await api.get('/bookings');
    return response.data;
  },
  
  getById: async (id: number) => {
    const response = await api.get(`/bookings/${id}`);
    return response.data;
  },
  
  cancel: async (id: number) => {
    const response = await api.patch(`/bookings/${id}/cancel`);
    return response.data;
  },
};

export default api;
