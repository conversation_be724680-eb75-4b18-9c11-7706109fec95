{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/laragon/www/nextjs/frontend/src/store/flightStore.ts"], "sourcesContent": ["import { create } from 'zustand';\n\nexport interface Flight {\n  id: number;\n  airline_name: string;\n  airline_code: string;\n  flight_number: string;\n  aircraft_type?: string;\n  departure: {\n    airport: string;\n    city: string;\n    country: string;\n    time: string;\n    formatted_time: string;\n    formatted_date: string;\n  };\n  arrival: {\n    airport: string;\n    city: string;\n    country: string;\n    time: string;\n    formatted_time: string;\n    formatted_date: string;\n  };\n  duration_minutes: number;\n  duration_formatted: string;\n  stops: number;\n  stop_airports?: string[];\n  prices: {\n    economy: number;\n    business?: number;\n    first?: number;\n  };\n  available_seats: {\n    economy: number;\n    business: number;\n    first: number;\n  };\n  airline_logo_url?: string;\n  amenities?: string[];\n  baggage_policy?: string;\n  cancellation_policy?: string;\n  is_refundable: boolean;\n}\n\nexport interface SearchParams {\n  from: string;\n  to: string;\n  departure_date: string;\n  return_date?: string;\n  passengers: number;\n  class: 'economy' | 'business' | 'first';\n  trip_type: 'one-way' | 'round-trip';\n}\n\ninterface FlightState {\n  flights: Flight[];\n  selectedFlight: Flight | null;\n  searchParams: SearchParams;\n  isLoading: boolean;\n  error: string | null;\n  setFlights: (flights: Flight[]) => void;\n  setSelectedFlight: (flight: Flight | null) => void;\n  setSearchParams: (params: Partial<SearchParams>) => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  clearFlights: () => void;\n}\n\nexport const useFlightStore = create<FlightState>((set) => ({\n  flights: [],\n  selectedFlight: null,\n  searchParams: {\n    from: '',\n    to: '',\n    departure_date: '',\n    return_date: '',\n    passengers: 1,\n    class: 'economy',\n    trip_type: 'one-way',\n  },\n  isLoading: false,\n  error: null,\n  setFlights: (flights) => set({ flights }),\n  setSelectedFlight: (flight) => set({ selectedFlight: flight }),\n  setSearchParams: (params) =>\n    set((state) => ({\n      searchParams: { ...state.searchParams, ...params },\n    })),\n  setLoading: (loading) => set({ isLoading: loading }),\n  setError: (error) => set({ error }),\n  clearFlights: () => set({ flights: [], selectedFlight: null }),\n}));\n"], "names": [], "mappings": ";;;AAAA;;AAqEO,MAAM,iBAAiB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAe,CAAC,MAAQ,CAAC;QAC1D,SAAS,EAAE;QACX,gBAAgB;QAChB,cAAc;YACZ,MAAM;YACN,IAAI;YACJ,gBAAgB;YAChB,aAAa;YACb,YAAY;YACZ,OAAO;YACP,WAAW;QACb;QACA,WAAW;QACX,OAAO;QACP,YAAY,CAAC,UAAY,IAAI;gBAAE;YAAQ;QACvC,mBAAmB,CAAC,SAAW,IAAI;gBAAE,gBAAgB;YAAO;QAC5D,iBAAiB,CAAC,SAChB,IAAI,CAAC,QAAU,CAAC;oBACd,cAAc;wBAAE,GAAG,MAAM,YAAY;wBAAE,GAAG,MAAM;oBAAC;gBACnD,CAAC;QACH,YAAY,CAAC,UAAY,IAAI;gBAAE,WAAW;YAAQ;QAClD,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;QACjC,cAAc,IAAM,IAAI;gBAAE,SAAS,EAAE;gBAAE,gBAAgB;YAAK;IAC9D,CAAC"}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/laragon/www/nextjs/frontend/src/store/authStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\n\ninterface User {\n  id: number;\n  name: string;\n  email: string;\n  email_verified_at?: string;\n  created_at: string;\n  updated_at: string;\n}\n\ninterface AuthState {\n  user: User | null;\n  token: string | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  setAuth: (user: User, token: string) => void;\n  logout: () => void;\n  setLoading: (loading: boolean) => void;\n}\n\nexport const useAuthStore = create<AuthState>()(\n  persist(\n    (set) => ({\n      user: null,\n      token: null,\n      isAuthenticated: false,\n      isLoading: false,\n      setAuth: (user, token) =>\n        set({\n          user,\n          token,\n          isAuthenticated: true,\n        }),\n      logout: () =>\n        set({\n          user: null,\n          token: null,\n          isAuthenticated: false,\n        }),\n      setLoading: (loading) => set({ isLoading: loading }),\n    }),\n    {\n      name: 'auth-storage',\n    }\n  )\n);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAqBO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,MAAM;QACN,OAAO;QACP,iBAAiB;QACjB,WAAW;QACX,SAAS,CAAC,MAAM,QACd,IAAI;gBACF;gBACA;gBACA,iBAAiB;YACnB;QACF,QAAQ,IACN,IAAI;gBACF,MAAM;gBACN,OAAO;gBACP,iBAAiB;YACnB;QACF,YAAY,CAAC,UAAY,IAAI;gBAAE,WAAW;YAAQ;IACpD,CAAC,GACD;IACE,MAAM;AACR"}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/laragon/www/nextjs/frontend/src/lib/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { useAuthStore } from '@/store/authStore';\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = useAuthStore.getState().token;\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor to handle auth errors\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      useAuthStore.getState().logout();\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Auth API\nexport const authAPI = {\n  register: async (data: { name: string; email: string; password: string; password_confirmation: string }) => {\n    const response = await api.post('/register', data);\n    return response.data;\n  },\n  \n  login: async (data: { email: string; password: string }) => {\n    const response = await api.post('/login', data);\n    return response.data;\n  },\n  \n  logout: async () => {\n    const response = await api.post('/logout');\n    return response.data;\n  },\n  \n  getUser: async () => {\n    const response = await api.get('/user');\n    return response.data;\n  },\n};\n\n// Flight API\nexport const flightAPI = {\n  search: async (params: {\n    from: string;\n    to: string;\n    departure_date: string;\n    return_date?: string;\n    passengers?: number;\n    class?: string;\n  }) => {\n    const response = await api.get('/flights/search', { params });\n    return response.data;\n  },\n  \n  getAll: async () => {\n    const response = await api.get('/flights');\n    return response.data;\n  },\n  \n  getById: async (id: number) => {\n    const response = await api.get(`/flights/${id}`);\n    return response.data;\n  },\n};\n\n// Booking API\nexport const bookingAPI = {\n  create: async (data: {\n    flight_id: number;\n    passenger_name: string;\n    passenger_email: string;\n    passenger_phone: string;\n    passenger_dob: string;\n    passenger_passport?: string;\n    class: string;\n    passengers_count?: number;\n    passenger_details?: any;\n  }) => {\n    const response = await api.post('/bookings', data);\n    return response.data;\n  },\n  \n  getAll: async () => {\n    const response = await api.get('/bookings');\n    return response.data;\n  },\n  \n  getById: async (id: number) => {\n    const response = await api.get(`/bookings/${id}`);\n    return response.data;\n  },\n  \n  cancel: async (id: number) => {\n    const response = await api.patch(`/bookings/${id}/cancel`);\n    return response.data;\n  },\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;AACA;AADA;;;AAGA,MAAM,eAAe,iEAAmC;AAExD,wBAAwB;AACxB,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;QAChB,UAAU;IACZ;AACF;AAEA,wCAAwC;AACxC,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAC1B,CAAC;IACC,MAAM,QAAQ,yHAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,KAAK;IAC3C,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,6CAA6C;AAC7C,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC,WAAa,UACd,CAAC;IACC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,yHAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,MAAM;QAC9B,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM,UAAU;IACrB,UAAU,OAAO;QACf,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,aAAa;QAC7C,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO,OAAO;QACZ,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,UAAU;QAC1C,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ;QACN,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC;QAChC,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;QACP,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,QAAQ,OAAO;QAQb,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,mBAAmB;YAAE;QAAO;QAC3D,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ;QACN,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI;QAC/C,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,aAAa;IACxB,QAAQ,OAAO;QAWb,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,aAAa;QAC7C,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ;QACN,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;QAChD,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,CAAC,UAAU,EAAE,GAAG,OAAO,CAAC;QACzD,OAAO,SAAS,IAAI;IACtB;AACF;uCAEe"}}, {"offset": {"line": 315, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 321, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/laragon/www/nextjs/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\nexport function formatCurrency(amount: number, currency = 'USD') {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount);\n}\n\nexport function formatDate(date: string | Date, format: 'short' | 'long' = 'short') {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  \n  if (format === 'long') {\n    return dateObj.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n    });\n  }\n  \n  return dateObj.toLocaleDateString('en-US', {\n    month: 'short',\n    day: 'numeric',\n    year: 'numeric',\n  });\n}\n\nexport function formatTime(date: string | Date) {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return dateObj.toLocaleTimeString('en-US', {\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: false,\n  });\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAEO,SAAS,eAAe,MAAc,EAAE,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB,EAAE,SAA2B,OAAO;IAChF,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAE5D,IAAI,WAAW,QAAQ;QACrB,OAAO,QAAQ,kBAAkB,CAAC,SAAS;YACzC,SAAS;YACT,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,OAAO,QAAQ,kBAAkB,CAAC,SAAS;QACzC,OAAO;QACP,KAAK;QACL,MAAM;IACR;AACF;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,QAAQ,kBAAkB,CAAC,SAAS;QACzC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;AACF"}}, {"offset": {"line": 362, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 368, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/laragon/www/nextjs/frontend/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';\n  size?: 'sm' | 'md' | 'lg';\n  isLoading?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', isLoading, children, disabled, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 disabled:pointer-events-none disabled:opacity-50';\n    \n    const variants = {\n      primary: 'bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800',\n      secondary: 'bg-secondary-100 text-secondary-900 hover:bg-secondary-200 active:bg-secondary-300',\n      outline: 'border border-secondary-300 bg-white text-secondary-900 hover:bg-secondary-50 active:bg-secondary-100',\n      ghost: 'text-secondary-700 hover:bg-secondary-100 active:bg-secondary-200',\n    };\n\n    const sizes = {\n      sm: 'h-8 px-3 text-sm',\n      md: 'h-10 px-4 text-sm',\n      lg: 'h-12 px-6 text-base',\n    };\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || isLoading}\n        {...props}\n      >\n        {isLoading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACzF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,2BACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG;uCAEN"}}, {"offset": {"line": 439, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 445, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/laragon/www/nextjs/frontend/src/components/layout/Navbar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { useRouter } from 'next/navigation';\nimport { useAuthStore } from '@/store/authStore';\nimport { authAPI } from '@/lib/api';\nimport Button from '@/components/ui/Button';\nimport { Plane, Menu, X, User, LogOut } from 'lucide-react';\n\nconst Navbar = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);\n  const { user, isAuthenticated, logout } = useAuthStore();\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    try {\n      await authAPI.logout();\n      logout();\n      router.push('/');\n    } catch (error) {\n      console.error('Logout error:', error);\n      logout(); // Force logout even if API call fails\n      router.push('/');\n    }\n  };\n\n  return (\n    <nav className=\"bg-white/95 backdrop-blur-sm border-b border-secondary-200 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"bg-primary-600 p-2 rounded-lg\">\n              <Plane className=\"h-6 w-6 text-white\" />\n            </div>\n            <span className=\"text-xl font-bold text-secondary-900\">FlightBooker</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            <Link href=\"/flights\" className=\"text-secondary-700 hover:text-primary-600 transition-colors\">\n              Flights\n            </Link>\n            <Link href=\"/bookings\" className=\"text-secondary-700 hover:text-primary-600 transition-colors\">\n              My Bookings\n            </Link>\n            \n            {isAuthenticated ? (\n              <div className=\"relative\">\n                <button\n                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}\n                  className=\"flex items-center space-x-2 text-secondary-700 hover:text-primary-600 transition-colors\"\n                >\n                  <User className=\"h-5 w-5\" />\n                  <span>{user?.name}</span>\n                </button>\n                \n                {isUserMenuOpen && (\n                  <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-secondary-200 py-1\">\n                    <Link\n                      href=\"/profile\"\n                      className=\"block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50\"\n                      onClick={() => setIsUserMenuOpen(false)}\n                    >\n                      Profile\n                    </Link>\n                    <Link\n                      href=\"/bookings\"\n                      className=\"block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50\"\n                      onClick={() => setIsUserMenuOpen(false)}\n                    >\n                      My Bookings\n                    </Link>\n                    <button\n                      onClick={handleLogout}\n                      className=\"w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2\"\n                    >\n                      <LogOut className=\"h-4 w-4\" />\n                      <span>Logout</span>\n                    </button>\n                  </div>\n                )}\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-4\">\n                <Link href=\"/login\">\n                  <Button variant=\"ghost\">Login</Button>\n                </Link>\n                <Link href=\"/register\">\n                  <Button>Sign Up</Button>\n                </Link>\n              </div>\n            )}\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"text-secondary-700 hover:text-primary-600 transition-colors\"\n            >\n              {isMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden py-4 border-t border-secondary-200\">\n            <div className=\"flex flex-col space-y-4\">\n              <Link\n                href=\"/flights\"\n                className=\"text-secondary-700 hover:text-primary-600 transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Flights\n              </Link>\n              <Link\n                href=\"/bookings\"\n                className=\"text-secondary-700 hover:text-primary-600 transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                My Bookings\n              </Link>\n              \n              {isAuthenticated ? (\n                <div className=\"flex flex-col space-y-2 pt-4 border-t border-secondary-200\">\n                  <span className=\"text-sm text-secondary-500\">Welcome, {user?.name}</span>\n                  <Link\n                    href=\"/profile\"\n                    className=\"text-secondary-700 hover:text-primary-600 transition-colors\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    Profile\n                  </Link>\n                  <button\n                    onClick={handleLogout}\n                    className=\"text-left text-red-600 hover:text-red-700 transition-colors\"\n                  >\n                    Logout\n                  </button>\n                </div>\n              ) : (\n                <div className=\"flex flex-col space-y-2 pt-4 border-t border-secondary-200\">\n                  <Link href=\"/login\" onClick={() => setIsMenuOpen(false)}>\n                    <Button variant=\"ghost\" className=\"w-full justify-start\">Login</Button>\n                  </Link>\n                  <Link href=\"/register\" onClick={() => setIsMenuOpen(false)}>\n                    <Button className=\"w-full\">Sign Up</Button>\n                  </Link>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AAUA,MAAM,SAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IACrD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,iHAAA,CAAA,UAAO,CAAC,MAAM;YACpB;YACA,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,UAAU,sCAAsC;YAChD,OAAO,IAAI,CAAC;QACd;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,8OAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;sCAIzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAA8D;;;;;;8CAG9F,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAA8D;;;;;;gCAI9F,gCACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,kBAAkB,CAAC;4CAClC,WAAU;;8DAEV,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAM,MAAM;;;;;;;;;;;;wCAGd,gCACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,kBAAkB;8DAClC;;;;;;8DAGD,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,kBAAkB;8DAClC;;;;;;8DAGD,8OAAC;oDACC,SAAS;oDACT,WAAU;;sEAEV,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;yDAMd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC,kIAAA,CAAA,UAAM;gDAAC,SAAQ;0DAAQ;;;;;;;;;;;sDAE1B,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC,kIAAA,CAAA,UAAM;0DAAC;;;;;;;;;;;;;;;;;;;;;;;sCAOhB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;0CAET,2BAAa,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAAe,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAM/D,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;4BAIA,gCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CAA6B;4CAAU,MAAM;;;;;;;kDAC7D,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc;kDAC9B;;;;;;kDAGD,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;qDAKH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,SAAS,IAAM,cAAc;kDAC/C,cAAA,8OAAC,kIAAA,CAAA,UAAM;4CAAC,SAAQ;4CAAQ,WAAU;sDAAuB;;;;;;;;;;;kDAE3D,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,SAAS,IAAM,cAAc;kDAClD,cAAA,8OAAC,kIAAA,CAAA,UAAM;4CAAC,WAAU;sDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU/C;uCAEe"}}, {"offset": {"line": 838, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 844, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/laragon/www/nextjs/frontend/src/components/flights/FlightCard.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { useRouter } from 'next/navigation';\nimport { Flight } from '@/store/flightStore';\nimport { formatCurrency } from '@/lib/utils';\nimport Button from '@/components/ui/Button';\nimport { Plane, Clock, MapPin, Wifi, Coffee, Tv } from 'lucide-react';\n\ninterface FlightCardProps {\n  flight: Flight;\n  selectedClass: 'economy' | 'business' | 'first';\n}\n\nconst FlightCard: React.FC<FlightCardProps> = ({ flight, selectedClass }) => {\n  const router = useRouter();\n\n  const handleBookFlight = () => {\n    router.push(`/flights/${flight.id}/book?class=${selectedClass}`);\n  };\n\n  const getPrice = () => {\n    return flight.prices[selectedClass] || flight.prices.economy;\n  };\n\n  const getAvailableSeats = () => {\n    return flight.available_seats[selectedClass] || flight.available_seats.economy;\n  };\n\n  const getAmenityIcon = (amenity: string) => {\n    switch (amenity.toLowerCase()) {\n      case 'wifi':\n        return <Wifi className=\"h-4 w-4\" />;\n      case 'entertainment':\n        return <Tv className=\"h-4 w-4\" />;\n      case 'meals':\n        return <Coffee className=\"h-4 w-4\" />;\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-lg border border-secondary-200 p-6 hover:shadow-xl transition-shadow\">\n      <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6\">\n        {/* Flight Info */}\n        <div className=\"flex-1\">\n          <div className=\"flex items-center gap-4 mb-4\">\n            {flight.airline_logo_url && (\n              <img\n                src={flight.airline_logo_url}\n                alt={flight.airline_name}\n                className=\"h-8 w-8 object-contain\"\n              />\n            )}\n            <div>\n              <h3 className=\"font-semibold text-secondary-900\">\n                {flight.airline_name} {flight.flight_number}\n              </h3>\n              <p className=\"text-sm text-secondary-600\">{flight.aircraft_type}</p>\n            </div>\n          </div>\n\n          {/* Route */}\n          <div className=\"flex items-center gap-4 mb-4\">\n            <div className=\"text-center\">\n              <p className=\"text-2xl font-bold text-secondary-900\">\n                {flight.departure.formatted_time}\n              </p>\n              <p className=\"text-sm text-secondary-600\">{flight.departure.airport}</p>\n              <p className=\"text-xs text-secondary-500\">{flight.departure.city}</p>\n            </div>\n\n            <div className=\"flex-1 flex items-center justify-center\">\n              <div className=\"flex items-center gap-2 text-secondary-500\">\n                <div className=\"h-px bg-secondary-300 flex-1\"></div>\n                <div className=\"text-center\">\n                  <Plane className=\"h-4 w-4 mx-auto mb-1\" />\n                  <p className=\"text-xs\">{flight.duration_formatted}</p>\n                  {flight.stops > 0 && (\n                    <p className=\"text-xs text-orange-600\">\n                      {flight.stops} stop{flight.stops > 1 ? 's' : ''}\n                    </p>\n                  )}\n                </div>\n                <div className=\"h-px bg-secondary-300 flex-1\"></div>\n              </div>\n            </div>\n\n            <div className=\"text-center\">\n              <p className=\"text-2xl font-bold text-secondary-900\">\n                {flight.arrival.formatted_time}\n              </p>\n              <p className=\"text-sm text-secondary-600\">{flight.arrival.airport}</p>\n              <p className=\"text-xs text-secondary-500\">{flight.arrival.city}</p>\n            </div>\n          </div>\n\n          {/* Amenities */}\n          {flight.amenities && flight.amenities.length > 0 && (\n            <div className=\"flex items-center gap-3 text-secondary-500\">\n              {flight.amenities.slice(0, 4).map((amenity, index) => (\n                <div key={index} className=\"flex items-center gap-1\">\n                  {getAmenityIcon(amenity)}\n                  <span className=\"text-xs\">{amenity}</span>\n                </div>\n              ))}\n              {flight.amenities.length > 4 && (\n                <span className=\"text-xs\">+{flight.amenities.length - 4} more</span>\n              )}\n            </div>\n          )}\n        </div>\n\n        {/* Price and Booking */}\n        <div className=\"lg:text-right\">\n          <div className=\"mb-4\">\n            <p className=\"text-3xl font-bold text-primary-600\">\n              {formatCurrency(getPrice())}\n            </p>\n            <p className=\"text-sm text-secondary-600\">per person</p>\n            <p className=\"text-xs text-secondary-500 capitalize\">\n              {selectedClass} class\n            </p>\n          </div>\n\n          <div className=\"mb-4\">\n            <p className=\"text-sm text-secondary-600\">\n              {getAvailableSeats()} seats left\n            </p>\n            {flight.is_refundable && (\n              <p className=\"text-xs text-green-600\">✓ Refundable</p>\n            )}\n          </div>\n\n          <Button\n            onClick={handleBookFlight}\n            size=\"lg\"\n            className=\"w-full lg:w-auto\"\n          >\n            Book Flight\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default FlightCard;\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAPA;;;;;;AAcA,MAAM,aAAwC,CAAC,EAAE,MAAM,EAAE,aAAa,EAAE;IACtE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,mBAAmB;QACvB,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,OAAO,EAAE,CAAC,YAAY,EAAE,eAAe;IACjE;IAEA,MAAM,WAAW;QACf,OAAO,OAAO,MAAM,CAAC,cAAc,IAAI,OAAO,MAAM,CAAC,OAAO;IAC9D;IAEA,MAAM,oBAAoB;QACxB,OAAO,OAAO,eAAe,CAAC,cAAc,IAAI,OAAO,eAAe,CAAC,OAAO;IAChF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ,QAAQ,WAAW;YACzB,KAAK;gBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,8LAAA,CAAA,KAAE;oBAAC,WAAU;;;;;;YACvB,KAAK;gBACH,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;gCACZ,OAAO,gBAAgB,kBACtB,8OAAC;oCACC,KAAK,OAAO,gBAAgB;oCAC5B,KAAK,OAAO,YAAY;oCACxB,WAAU;;;;;;8CAGd,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;gDACX,OAAO,YAAY;gDAAC;gDAAE,OAAO,aAAa;;;;;;;sDAE7C,8OAAC;4CAAE,WAAU;sDAA8B,OAAO,aAAa;;;;;;;;;;;;;;;;;;sCAKnE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDACV,OAAO,SAAS,CAAC,cAAc;;;;;;sDAElC,8OAAC;4CAAE,WAAU;sDAA8B,OAAO,SAAS,CAAC,OAAO;;;;;;sDACnE,8OAAC;4CAAE,WAAU;sDAA8B,OAAO,SAAS,CAAC,IAAI;;;;;;;;;;;;8CAGlE,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAE,WAAU;kEAAW,OAAO,kBAAkB;;;;;;oDAChD,OAAO,KAAK,GAAG,mBACd,8OAAC;wDAAE,WAAU;;4DACV,OAAO,KAAK;4DAAC;4DAAM,OAAO,KAAK,GAAG,IAAI,MAAM;;;;;;;;;;;;;0DAInD,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;8CAInB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDACV,OAAO,OAAO,CAAC,cAAc;;;;;;sDAEhC,8OAAC;4CAAE,WAAU;sDAA8B,OAAO,OAAO,CAAC,OAAO;;;;;;sDACjE,8OAAC;4CAAE,WAAU;sDAA8B,OAAO,OAAO,CAAC,IAAI;;;;;;;;;;;;;;;;;;wBAKjE,OAAO,SAAS,IAAI,OAAO,SAAS,CAAC,MAAM,GAAG,mBAC7C,8OAAC;4BAAI,WAAU;;gCACZ,OAAO,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBAC1C,8OAAC;wCAAgB,WAAU;;4CACxB,eAAe;0DAChB,8OAAC;gDAAK,WAAU;0DAAW;;;;;;;uCAFnB;;;;;gCAKX,OAAO,SAAS,CAAC,MAAM,GAAG,mBACzB,8OAAC;oCAAK,WAAU;;wCAAU;wCAAE,OAAO,SAAS,CAAC,MAAM,GAAG;wCAAE;;;;;;;;;;;;;;;;;;;8BAOhE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CACV,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;8CAElB,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,8OAAC;oCAAE,WAAU;;wCACV;wCAAc;;;;;;;;;;;;;sCAInB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;wCACV;wCAAoB;;;;;;;gCAEtB,OAAO,aAAa,kBACnB,8OAAC;oCAAE,WAAU;8CAAyB;;;;;;;;;;;;sCAI1C,8OAAC,kIAAA,CAAA,UAAM;4BACL,SAAS;4BACT,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;uCAEe"}}, {"offset": {"line": 1238, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1244, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/laragon/www/nextjs/frontend/src/app/flights/search/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useFlightStore } from '@/store/flightStore';\nimport Navbar from '@/components/layout/Navbar';\nimport FlightCard from '@/components/flights/FlightCard';\nimport Button from '@/components/ui/Button';\nimport { Filter, SortAsc, Plane } from 'lucide-react';\n\nconst FlightSearchPage = () => {\n  const { flights, searchParams, isLoading, error } = useFlightStore();\n  const [sortBy, setSortBy] = useState<'price' | 'duration' | 'departure'>('price');\n  const [filterClass, setFilterClass] = useState<'economy' | 'business' | 'first'>(searchParams.class);\n  const [showFilters, setShowFilters] = useState(false);\n\n  const sortedFlights = [...flights].sort((a, b) => {\n    switch (sortBy) {\n      case 'price':\n        const priceA = a.prices[filterClass] || a.prices.economy;\n        const priceB = b.prices[filterClass] || b.prices.economy;\n        return priceA - priceB;\n      case 'duration':\n        return a.duration_minutes - b.duration_minutes;\n      case 'departure':\n        return new Date(a.departure.time).getTime() - new Date(b.departure.time).getTime();\n      default:\n        return 0;\n    }\n  });\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-secondary-50\">\n        <Navbar />\n        <div className=\"flex items-center justify-center py-20\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4\"></div>\n            <p className=\"text-secondary-600\">Searching for flights...</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-secondary-50\">\n        <Navbar />\n        <div className=\"flex items-center justify-center py-20\">\n          <div className=\"text-center\">\n            <div className=\"bg-red-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <Plane className=\"h-8 w-8 text-red-600\" />\n            </div>\n            <h2 className=\"text-2xl font-bold text-secondary-900 mb-2\">Search Error</h2>\n            <p className=\"text-secondary-600 mb-4\">{error}</p>\n            <Button onClick={() => window.location.href = '/'}>\n              Search Again\n            </Button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-secondary-50\">\n      <Navbar />\n      \n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Search Summary */}\n        <div className=\"bg-white rounded-xl shadow-sm p-6 mb-6\">\n          <div className=\"flex flex-col md:flex-row md:items-center md:justify-between gap-4\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-secondary-900\">\n                {searchParams.from} → {searchParams.to}\n              </h1>\n              <p className=\"text-secondary-600\">\n                {searchParams.departure_date} • {searchParams.passengers} passenger{searchParams.passengers > 1 ? 's' : ''} • {searchParams.class} class\n              </p>\n            </div>\n            <div className=\"text-right\">\n              <p className=\"text-lg font-semibold text-secondary-900\">\n                {flights.length} flight{flights.length !== 1 ? 's' : ''} found\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"flex flex-col lg:flex-row gap-6\">\n          {/* Filters Sidebar */}\n          <div className=\"lg:w-80\">\n            <div className=\"bg-white rounded-xl shadow-sm p-6 sticky top-6\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h3 className=\"text-lg font-semibold text-secondary-900\">Filters & Sort</h3>\n                <button\n                  onClick={() => setShowFilters(!showFilters)}\n                  className=\"lg:hidden\"\n                >\n                  <Filter className=\"h-5 w-5\" />\n                </button>\n              </div>\n\n              <div className={`space-y-6 ${showFilters ? 'block' : 'hidden lg:block'}`}>\n                {/* Sort Options */}\n                <div>\n                  <label className=\"block text-sm font-medium text-secondary-700 mb-2\">\n                    Sort by\n                  </label>\n                  <select\n                    value={sortBy}\n                    onChange={(e) => setSortBy(e.target.value as any)}\n                    className=\"w-full border border-secondary-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500\"\n                  >\n                    <option value=\"price\">Price (Low to High)</option>\n                    <option value=\"duration\">Duration (Shortest)</option>\n                    <option value=\"departure\">Departure Time</option>\n                  </select>\n                </div>\n\n                {/* Class Filter */}\n                <div>\n                  <label className=\"block text-sm font-medium text-secondary-700 mb-2\">\n                    Class\n                  </label>\n                  <div className=\"space-y-2\">\n                    {['economy', 'business', 'first'].map((classType) => (\n                      <label key={classType} className=\"flex items-center\">\n                        <input\n                          type=\"radio\"\n                          name=\"class\"\n                          value={classType}\n                          checked={filterClass === classType}\n                          onChange={(e) => setFilterClass(e.target.value as any)}\n                          className=\"mr-2 text-primary-600 focus:ring-primary-500\"\n                        />\n                        <span className=\"text-sm text-secondary-700 capitalize\">\n                          {classType} Class\n                        </span>\n                      </label>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Price Range */}\n                <div>\n                  <label className=\"block text-sm font-medium text-secondary-700 mb-2\">\n                    Price Range\n                  </label>\n                  <div className=\"text-sm text-secondary-600\">\n                    {flights.length > 0 && (\n                      <>\n                        ${Math.min(...flights.map(f => f.prices[filterClass] || f.prices.economy))} - \n                        ${Math.max(...flights.map(f => f.prices[filterClass] || f.prices.economy))}\n                      </>\n                    )}\n                  </div>\n                </div>\n\n                {/* Stops Filter */}\n                <div>\n                  <label className=\"block text-sm font-medium text-secondary-700 mb-2\">\n                    Stops\n                  </label>\n                  <div className=\"space-y-2\">\n                    <label className=\"flex items-center\">\n                      <input\n                        type=\"checkbox\"\n                        className=\"mr-2 text-primary-600 focus:ring-primary-500\"\n                        defaultChecked\n                      />\n                      <span className=\"text-sm text-secondary-700\">Direct flights</span>\n                    </label>\n                    <label className=\"flex items-center\">\n                      <input\n                        type=\"checkbox\"\n                        className=\"mr-2 text-primary-600 focus:ring-primary-500\"\n                        defaultChecked\n                      />\n                      <span className=\"text-sm text-secondary-700\">1 stop</span>\n                    </label>\n                    <label className=\"flex items-center\">\n                      <input\n                        type=\"checkbox\"\n                        className=\"mr-2 text-primary-600 focus:ring-primary-500\"\n                        defaultChecked\n                      />\n                      <span className=\"text-sm text-secondary-700\">2+ stops</span>\n                    </label>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Flight Results */}\n          <div className=\"flex-1\">\n            {sortedFlights.length === 0 ? (\n              <div className=\"bg-white rounded-xl shadow-sm p-12 text-center\">\n                <div className=\"bg-secondary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <Plane className=\"h-8 w-8 text-secondary-600\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-secondary-900 mb-2\">\n                  No flights found\n                </h3>\n                <p className=\"text-secondary-600 mb-4\">\n                  Try adjusting your search criteria or dates\n                </p>\n                <Button onClick={() => window.location.href = '/'}>\n                  New Search\n                </Button>\n              </div>\n            ) : (\n              <div className=\"space-y-4\">\n                {sortedFlights.map((flight) => (\n                  <FlightCard\n                    key={flight.id}\n                    flight={flight}\n                    selectedClass={filterClass}\n                  />\n                ))}\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default FlightSearchPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAPA;;;;;;;;AASA,MAAM,mBAAmB;IACvB,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,iBAAc,AAAD;IACjE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsC;IACzE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoC,aAAa,KAAK;IACnG,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,gBAAgB;WAAI;KAAQ,CAAC,IAAI,CAAC,CAAC,GAAG;QAC1C,OAAQ;YACN,KAAK;gBACH,MAAM,SAAS,EAAE,MAAM,CAAC,YAAY,IAAI,EAAE,MAAM,CAAC,OAAO;gBACxD,MAAM,SAAS,EAAE,MAAM,CAAC,YAAY,IAAI,EAAE,MAAM,CAAC,OAAO;gBACxD,OAAO,SAAS;YAClB,KAAK;gBACH,OAAO,EAAE,gBAAgB,GAAG,EAAE,gBAAgB;YAChD,KAAK;gBACH,OAAO,IAAI,KAAK,EAAE,SAAS,CAAC,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,CAAC,IAAI,EAAE,OAAO;YAClF;gBACE,OAAO;QACX;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,sIAAA,CAAA,UAAM;;;;;8BACP,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;;;;;;;;;;;;IAK5C;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,sIAAA,CAAA,UAAM;;;;;8BACP,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,8OAAC;gCAAG,WAAU;0CAA6C;;;;;;0CAC3D,8OAAC;gCAAE,WAAU;0CAA2B;;;;;;0CACxC,8OAAC,kIAAA,CAAA,UAAM;gCAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;0CAAK;;;;;;;;;;;;;;;;;;;;;;;IAO7D;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,UAAM;;;;;0BAEP,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;gDACX,aAAa,IAAI;gDAAC;gDAAI,aAAa,EAAE;;;;;;;sDAExC,8OAAC;4CAAE,WAAU;;gDACV,aAAa,cAAc;gDAAC;gDAAI,aAAa,UAAU;gDAAC;gDAAW,aAAa,UAAU,GAAG,IAAI,MAAM;gDAAG;gDAAI,aAAa,KAAK;gDAAC;;;;;;;;;;;;;8CAGtI,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;4CACV,QAAQ,MAAM;4CAAC;4CAAQ,QAAQ,MAAM,KAAK,IAAI,MAAM;4CAAG;;;;;;;;;;;;;;;;;;;;;;;kCAMhE,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDACC,SAAS,IAAM,eAAe,CAAC;oDAC/B,WAAU;8DAEV,cAAA,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAItB,8OAAC;4CAAI,WAAW,CAAC,UAAU,EAAE,cAAc,UAAU,mBAAmB;;8DAEtE,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAAoD;;;;;;sEAGrE,8OAAC;4DACC,OAAO;4DACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;4DACzC,WAAU;;8EAEV,8OAAC;oEAAO,OAAM;8EAAQ;;;;;;8EACtB,8OAAC;oEAAO,OAAM;8EAAW;;;;;;8EACzB,8OAAC;oEAAO,OAAM;8EAAY;;;;;;;;;;;;;;;;;;8DAK9B,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAAoD;;;;;;sEAGrE,8OAAC;4DAAI,WAAU;sEACZ;gEAAC;gEAAW;gEAAY;6DAAQ,CAAC,GAAG,CAAC,CAAC,0BACrC,8OAAC;oEAAsB,WAAU;;sFAC/B,8OAAC;4EACC,MAAK;4EACL,MAAK;4EACL,OAAO;4EACP,SAAS,gBAAgB;4EACzB,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4EAC9C,WAAU;;;;;;sFAEZ,8OAAC;4EAAK,WAAU;;gFACb;gFAAU;;;;;;;;mEAVH;;;;;;;;;;;;;;;;8DAkBlB,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAAoD;;;;;;sEAGrE,8OAAC;4DAAI,WAAU;sEACZ,QAAQ,MAAM,GAAG,mBAChB;;oEAAE;oEACE,KAAK,GAAG,IAAI,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM,CAAC,YAAY,IAAI,EAAE,MAAM,CAAC,OAAO;oEAAG;oEACzE,KAAK,GAAG,IAAI,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM,CAAC,YAAY,IAAI,EAAE,MAAM,CAAC,OAAO;;;;;;;;;;;;;;8DAOhF,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAAoD;;;;;;sEAGrE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAM,WAAU;;sFACf,8OAAC;4EACC,MAAK;4EACL,WAAU;4EACV,cAAc;;;;;;sFAEhB,8OAAC;4EAAK,WAAU;sFAA6B;;;;;;;;;;;;8EAE/C,8OAAC;oEAAM,WAAU;;sFACf,8OAAC;4EACC,MAAK;4EACL,WAAU;4EACV,cAAc;;;;;;sFAEhB,8OAAC;4EAAK,WAAU;sFAA6B;;;;;;;;;;;;8EAE/C,8OAAC;oEAAM,WAAU;;sFACf,8OAAC;4EACC,MAAK;4EACL,WAAU;4EACV,cAAc;;;;;;sFAEhB,8OAAC;4EAAK,WAAU;sFAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASzD,8OAAC;gCAAI,WAAU;0CACZ,cAAc,MAAM,KAAK,kBACxB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAG,WAAU;sDAAgD;;;;;;sDAG9D,8OAAC;4CAAE,WAAU;sDAA0B;;;;;;sDAGvC,8OAAC,kIAAA,CAAA,UAAM;4CAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;sDAAK;;;;;;;;;;;yDAKrD,8OAAC;oCAAI,WAAU;8CACZ,cAAc,GAAG,CAAC,CAAC,uBAClB,8OAAC,2IAAA,CAAA,UAAU;4CAET,QAAQ;4CACR,eAAe;2CAFV,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYlC;uCAEe"}}, {"offset": {"line": 1877, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}