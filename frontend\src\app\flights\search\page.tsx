'use client';

import React, { useState, useEffect } from 'react';
import { useFlightStore } from '@/store/flightStore';
import Navbar from '@/components/layout/Navbar';
import FlightCard from '@/components/flights/FlightCard';
import Button from '@/components/ui/Button';
import { Filter, SortAsc, Plane } from 'lucide-react';

const FlightSearchPage = () => {
  const { flights, searchParams, isLoading, error } = useFlightStore();
  const [sortBy, setSortBy] = useState<'price' | 'duration' | 'departure'>('price');
  const [filterClass, setFilterClass] = useState<'economy' | 'business' | 'first'>(searchParams.class);
  const [showFilters, setShowFilters] = useState(false);

  const sortedFlights = [...flights].sort((a, b) => {
    switch (sortBy) {
      case 'price':
        const priceA = a.prices[filterClass] || a.prices.economy;
        const priceB = b.prices[filterClass] || b.prices.economy;
        return priceA - priceB;
      case 'duration':
        return a.duration_minutes - b.duration_minutes;
      case 'departure':
        return new Date(a.departure.time).getTime() - new Date(b.departure.time).getTime();
      default:
        return 0;
    }
  });

  if (isLoading) {
    return (
      <div className="min-h-screen bg-secondary-50">
        <Navbar />
        <div className="flex items-center justify-center py-20">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-secondary-600">Searching for flights...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-secondary-50">
        <Navbar />
        <div className="flex items-center justify-center py-20">
          <div className="text-center">
            <div className="bg-red-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Plane className="h-8 w-8 text-red-600" />
            </div>
            <h2 className="text-2xl font-bold text-secondary-900 mb-2">Search Error</h2>
            <p className="text-secondary-600 mb-4">{error}</p>
            <Button onClick={() => window.location.href = '/'}>
              Search Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-secondary-50">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search Summary */}
        <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-bold text-secondary-900">
                {searchParams.from} → {searchParams.to}
              </h1>
              <p className="text-secondary-600">
                {searchParams.departure_date} • {searchParams.passengers} passenger{searchParams.passengers > 1 ? 's' : ''} • {searchParams.class} class
              </p>
            </div>
            <div className="text-right">
              <p className="text-lg font-semibold text-secondary-900">
                {flights.length} flight{flights.length !== 1 ? 's' : ''} found
              </p>
            </div>
          </div>
        </div>

        <div className="flex flex-col lg:flex-row gap-6">
          {/* Filters Sidebar */}
          <div className="lg:w-80">
            <div className="bg-white rounded-xl shadow-sm p-6 sticky top-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-secondary-900">Filters & Sort</h3>
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="lg:hidden"
                >
                  <Filter className="h-5 w-5" />
                </button>
              </div>

              <div className={`space-y-6 ${showFilters ? 'block' : 'hidden lg:block'}`}>
                {/* Sort Options */}
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">
                    Sort by
                  </label>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value as any)}
                    className="w-full border border-secondary-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="price">Price (Low to High)</option>
                    <option value="duration">Duration (Shortest)</option>
                    <option value="departure">Departure Time</option>
                  </select>
                </div>

                {/* Class Filter */}
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">
                    Class
                  </label>
                  <div className="space-y-2">
                    {['economy', 'business', 'first'].map((classType) => (
                      <label key={classType} className="flex items-center">
                        <input
                          type="radio"
                          name="class"
                          value={classType}
                          checked={filterClass === classType}
                          onChange={(e) => setFilterClass(e.target.value as any)}
                          className="mr-2 text-primary-600 focus:ring-primary-500"
                        />
                        <span className="text-sm text-secondary-700 capitalize">
                          {classType} Class
                        </span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Price Range */}
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">
                    Price Range
                  </label>
                  <div className="text-sm text-secondary-600">
                    {flights.length > 0 && (
                      <>
                        ${Math.min(...flights.map(f => f.prices[filterClass] || f.prices.economy))} - 
                        ${Math.max(...flights.map(f => f.prices[filterClass] || f.prices.economy))}
                      </>
                    )}
                  </div>
                </div>

                {/* Stops Filter */}
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-2">
                    Stops
                  </label>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        className="mr-2 text-primary-600 focus:ring-primary-500"
                        defaultChecked
                      />
                      <span className="text-sm text-secondary-700">Direct flights</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        className="mr-2 text-primary-600 focus:ring-primary-500"
                        defaultChecked
                      />
                      <span className="text-sm text-secondary-700">1 stop</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        className="mr-2 text-primary-600 focus:ring-primary-500"
                        defaultChecked
                      />
                      <span className="text-sm text-secondary-700">2+ stops</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Flight Results */}
          <div className="flex-1">
            {sortedFlights.length === 0 ? (
              <div className="bg-white rounded-xl shadow-sm p-12 text-center">
                <div className="bg-secondary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Plane className="h-8 w-8 text-secondary-600" />
                </div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-2">
                  No flights found
                </h3>
                <p className="text-secondary-600 mb-4">
                  Try adjusting your search criteria or dates
                </p>
                <Button onClick={() => window.location.href = '/'}>
                  New Search
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {sortedFlights.map((flight) => (
                  <FlightCard
                    key={flight.id}
                    flight={flight}
                    selectedClass={filterClass}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FlightSearchPage;
