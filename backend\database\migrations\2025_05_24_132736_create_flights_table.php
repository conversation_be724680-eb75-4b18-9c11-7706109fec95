<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('flights', function (Blueprint $table) {
            $table->id();
            $table->string('airline_name');
            $table->string('airline_code');
            $table->string('flight_number');
            $table->string('aircraft_type')->nullable();
            $table->string('departure_airport');
            $table->string('departure_city');
            $table->string('departure_country');
            $table->string('arrival_airport');
            $table->string('arrival_city');
            $table->string('arrival_country');
            $table->dateTime('departure_time');
            $table->dateTime('arrival_time');
            $table->integer('duration_minutes');
            $table->integer('stops')->default(0);
            $table->json('stop_airports')->nullable();
            $table->decimal('price_economy', 10, 2);
            $table->decimal('price_business', 10, 2)->nullable();
            $table->decimal('price_first', 10, 2)->nullable();
            $table->integer('available_seats_economy')->default(0);
            $table->integer('available_seats_business')->default(0);
            $table->integer('available_seats_first')->default(0);
            $table->string('airline_logo_url')->nullable();
            $table->json('amenities')->nullable();
            $table->text('baggage_policy')->nullable();
            $table->text('cancellation_policy')->nullable();
            $table->boolean('is_refundable')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('flights');
    }
};
