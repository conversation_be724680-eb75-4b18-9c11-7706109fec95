globalThis.__RSC_MANIFEST = globalThis.__RSC_MANIFEST || {};
globalThis.__RSC_MANIFEST["/flights/search/page"] = {"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"clientModules":{"[project]/node_modules/next/dist/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_a91c21._.js","static/chunks/src_app_favicon_ico_mjs_ddfdf0._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_a91c21._.js","static/chunks/src_app_favicon_ico_mjs_ddfdf0._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_a91c21._.js","static/chunks/src_app_favicon_ico_mjs_ddfdf0._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_a91c21._.js","static/chunks/src_app_favicon_ico_mjs_ddfdf0._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_a91c21._.js","static/chunks/src_app_favicon_ico_mjs_ddfdf0._.js"],"async":false},"[project]/src/app/flights/search/page.tsx":{"id":"[project]/src/app/flights/search/page.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_app_layout_tsx_20a9fc._.js","static/chunks/src_app_layout_tsx_61af54._.js","static/chunks/src_69c328._.js","static/chunks/node_modules_1adb94._.js","static/chunks/src_app_flights_search_page_tsx_fd3419._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_a91c21._.js","static/chunks/src_app_favicon_ico_mjs_ddfdf0._.js"],"async":false},"[project]/node_modules/next/dist/esm/lib/metadata/metadata-boundary.js":{"id":"[project]/node_modules/next/dist/lib/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_a91c21._.js","static/chunks/src_app_favicon_ico_mjs_ddfdf0._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_a91c21._.js","static/chunks/src_app_favicon_ico_mjs_ddfdf0._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_a91c21._.js","static/chunks/src_app_favicon_ico_mjs_ddfdf0._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_a91c21._.js","static/chunks/src_app_favicon_ico_mjs_ddfdf0._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_a91c21._.js","static/chunks/src_app_favicon_ico_mjs_ddfdf0._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_a91c21._.js","static/chunks/src_app_favicon_ico_mjs_ddfdf0._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/layout-router.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_a91c21._.js","static/chunks/src_app_favicon_ico_mjs_ddfdf0._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_a91c21._.js","static/chunks/src_app_favicon_ico_mjs_ddfdf0._.js"],"async":false},"[project]/node_modules/next/dist/esm/lib/metadata/metadata-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/lib/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_a91c21._.js","static/chunks/src_app_favicon_ico_mjs_ddfdf0._.js"],"async":false},"[project]/src/app/flights/search/page.tsx <module evaluation>":{"id":"[project]/src/app/flights/search/page.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_app_layout_tsx_20a9fc._.js","static/chunks/src_app_layout_tsx_61af54._.js","static/chunks/src_69c328._.js","static/chunks/node_modules_1adb94._.js","static/chunks/src_app_flights_search_page_tsx_fd3419._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_a91c21._.js","static/chunks/src_app_favicon_ico_mjs_ddfdf0._.js"],"async":false}},"ssrModuleMapping":{"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_36e229._.js","server/chunks/ssr/[root of the server]__0702e5._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_36e229._.js","server/chunks/ssr/[root of the server]__0702e5._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_36e229._.js","server/chunks/ssr/[root of the server]__0702e5._.js"],"async":false}},"[project]/node_modules/next/dist/lib/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/lib/metadata/metadata-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_36e229._.js","server/chunks/ssr/[root of the server]__0702e5._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_36e229._.js","server/chunks/ssr/[root of the server]__0702e5._.js"],"async":false}},"[project]/src/app/flights/search/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/flights/search/page.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root of the server]__e58c91._.js","server/chunks/ssr/node_modules_59084e._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_36e229._.js","server/chunks/ssr/[root of the server]__0702e5._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_36e229._.js","server/chunks/ssr/[root of the server]__0702e5._.js"],"async":false}}},"edgeSSRModuleMapping":{},"rscModuleMapping":{"[project]/node_modules/next/dist/lib/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/lib/metadata/metadata-boundary.js (client proxy)","name":"*","chunks":["server/app/flights/search/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js (client proxy)","name":"*","chunks":["server/app/flights/search/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js (client proxy)","name":"*","chunks":["server/app/flights/search/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js (client proxy)","name":"*","chunks":["server/app/flights/search/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js (client proxy)","name":"*","chunks":["server/app/flights/search/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js (client proxy)","name":"*","chunks":["server/app/flights/search/page.js"],"async":false}},"[project]/src/app/flights/search/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/flights/search/page.tsx (client proxy)","name":"*","chunks":["server/app/flights/search/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js (client proxy)","name":"*","chunks":["server/app/flights/search/page.js"],"async":false}}},"edgeRscModuleMapping":{},"entryCSSFiles":{"[project]/src/app/layout":[{"path":"static/chunks/[root of the server]__9a168d._.css","inlined":false}],"[project]/src/app/flights/search/page":[{"path":"static/chunks/[root of the server]__9a168d._.css","inlined":false}],"[project]/src/app/favicon.ico":[]},"entryJSFiles":{"[project]/src/app/layout":["static/chunks/src_app_layout_tsx_20a9fc._.js","static/chunks/src_app_layout_tsx_61af54._.js"],"[project]/src/app/flights/search/page":["static/chunks/src_app_layout_tsx_20a9fc._.js","static/chunks/src_app_layout_tsx_61af54._.js","static/chunks/src_69c328._.js","static/chunks/node_modules_1adb94._.js","static/chunks/src_app_flights_search_page_tsx_fd3419._.js"],"[project]/src/app/favicon.ico":["static/chunks/_a91c21._.js","static/chunks/src_app_favicon_ico_mjs_ddfdf0._.js"]}}
