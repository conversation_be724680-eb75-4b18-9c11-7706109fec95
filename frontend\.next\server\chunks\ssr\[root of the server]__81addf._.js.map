{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/laragon/www/nextjs/frontend/src/store/authStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\n\ninterface User {\n  id: number;\n  name: string;\n  email: string;\n  email_verified_at?: string;\n  created_at: string;\n  updated_at: string;\n}\n\ninterface AuthState {\n  user: User | null;\n  token: string | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  setAuth: (user: User, token: string) => void;\n  logout: () => void;\n  setLoading: (loading: boolean) => void;\n}\n\nexport const useAuthStore = create<AuthState>()(\n  persist(\n    (set) => ({\n      user: null,\n      token: null,\n      isAuthenticated: false,\n      isLoading: false,\n      setAuth: (user, token) =>\n        set({\n          user,\n          token,\n          isAuthenticated: true,\n        }),\n      logout: () =>\n        set({\n          user: null,\n          token: null,\n          isAuthenticated: false,\n        }),\n      setLoading: (loading) => set({ isLoading: loading }),\n    }),\n    {\n      name: 'auth-storage',\n    }\n  )\n);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAqBO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,MAAM;QACN,OAAO;QACP,iBAAiB;QACjB,WAAW;QACX,SAAS,CAAC,MAAM,QACd,IAAI;gBACF;gBACA;gBACA,iBAAiB;YACnB;QACF,QAAQ,IACN,IAAI;gBACF,MAAM;gBACN,OAAO;gBACP,iBAAiB;YACnB;QACF,YAAY,CAAC,UAAY,IAAI;gBAAE,WAAW;YAAQ;IACpD,CAAC,GACD;IACE,MAAM;AACR"}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/laragon/www/nextjs/frontend/src/lib/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { useAuthStore } from '@/store/authStore';\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = useAuthStore.getState().token;\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor to handle auth errors\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      useAuthStore.getState().logout();\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Auth API\nexport const authAPI = {\n  register: async (data: { name: string; email: string; password: string; password_confirmation: string }) => {\n    const response = await api.post('/register', data);\n    return response.data;\n  },\n  \n  login: async (data: { email: string; password: string }) => {\n    const response = await api.post('/login', data);\n    return response.data;\n  },\n  \n  logout: async () => {\n    const response = await api.post('/logout');\n    return response.data;\n  },\n  \n  getUser: async () => {\n    const response = await api.get('/user');\n    return response.data;\n  },\n};\n\n// Flight API\nexport const flightAPI = {\n  search: async (params: {\n    from: string;\n    to: string;\n    departure_date: string;\n    return_date?: string;\n    passengers?: number;\n    class?: string;\n  }) => {\n    const response = await api.get('/flights/search', { params });\n    return response.data;\n  },\n  \n  getAll: async () => {\n    const response = await api.get('/flights');\n    return response.data;\n  },\n  \n  getById: async (id: number) => {\n    const response = await api.get(`/flights/${id}`);\n    return response.data;\n  },\n};\n\n// Booking API\nexport const bookingAPI = {\n  create: async (data: {\n    flight_id: number;\n    passenger_name: string;\n    passenger_email: string;\n    passenger_phone: string;\n    passenger_dob: string;\n    passenger_passport?: string;\n    class: string;\n    passengers_count?: number;\n    passenger_details?: any;\n  }) => {\n    const response = await api.post('/bookings', data);\n    return response.data;\n  },\n  \n  getAll: async () => {\n    const response = await api.get('/bookings');\n    return response.data;\n  },\n  \n  getById: async (id: number) => {\n    const response = await api.get(`/bookings/${id}`);\n    return response.data;\n  },\n  \n  cancel: async (id: number) => {\n    const response = await api.patch(`/bookings/${id}/cancel`);\n    return response.data;\n  },\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;AACA;AADA;;;AAGA,MAAM,eAAe,iEAAmC;AAExD,wBAAwB;AACxB,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;QAChB,UAAU;IACZ;AACF;AAEA,wCAAwC;AACxC,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAC1B,CAAC;IACC,MAAM,QAAQ,yHAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,KAAK;IAC3C,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,6CAA6C;AAC7C,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC,WAAa,UACd,CAAC;IACC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,yHAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,MAAM;QAC9B,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM,UAAU;IACrB,UAAU,OAAO;QACf,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,aAAa;QAC7C,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO,OAAO;QACZ,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,UAAU;QAC1C,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ;QACN,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC;QAChC,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;QACP,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,QAAQ,OAAO;QAQb,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,mBAAmB;YAAE;QAAO;QAC3D,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ;QACN,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI;QAC/C,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,aAAa;IACxB,QAAQ,OAAO;QAWb,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,aAAa;QAC7C,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ;QACN,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;QAChD,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ,OAAO;QACb,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,CAAC,UAAU,EAAE,GAAG,OAAO,CAAC;QACzD,OAAO,SAAS,IAAI;IACtB;AACF;uCAEe"}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/laragon/www/nextjs/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\nexport function formatCurrency(amount: number, currency = 'USD') {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount);\n}\n\nexport function formatDate(date: string | Date, format: 'short' | 'long' = 'short') {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  \n  if (format === 'long') {\n    return dateObj.toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n    });\n  }\n  \n  return dateObj.toLocaleDateString('en-US', {\n    month: 'short',\n    day: 'numeric',\n    year: 'numeric',\n  });\n}\n\nexport function formatTime(date: string | Date) {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return dateObj.toLocaleTimeString('en-US', {\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: false,\n  });\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAEO,SAAS,eAAe,MAAc,EAAE,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB,EAAE,SAA2B,OAAO;IAChF,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAE5D,IAAI,WAAW,QAAQ;QACrB,OAAO,QAAQ,kBAAkB,CAAC,SAAS;YACzC,SAAS;YACT,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,OAAO,QAAQ,kBAAkB,CAAC,SAAS;QACzC,OAAO;QACP,KAAK;QACL,MAAM;IACR;AACF;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,QAAQ,kBAAkB,CAAC,SAAS;QACzC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;AACF"}}, {"offset": {"line": 314, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 320, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/laragon/www/nextjs/frontend/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';\n  size?: 'sm' | 'md' | 'lg';\n  isLoading?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', isLoading, children, disabled, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 disabled:pointer-events-none disabled:opacity-50';\n    \n    const variants = {\n      primary: 'bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800',\n      secondary: 'bg-secondary-100 text-secondary-900 hover:bg-secondary-200 active:bg-secondary-300',\n      outline: 'border border-secondary-300 bg-white text-secondary-900 hover:bg-secondary-50 active:bg-secondary-100',\n      ghost: 'text-secondary-700 hover:bg-secondary-100 active:bg-secondary-200',\n    };\n\n    const sizes = {\n      sm: 'h-8 px-3 text-sm',\n      md: 'h-10 px-4 text-sm',\n      lg: 'h-12 px-6 text-base',\n    };\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || isLoading}\n        {...props}\n      >\n        {isLoading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACzF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,2BACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG;uCAEN"}}, {"offset": {"line": 391, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 397, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/laragon/www/nextjs/frontend/src/components/ui/Input.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string;\n  error?: string;\n  icon?: React.ReactNode;\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, label, error, icon, type, ...props }, ref) => {\n    return (\n      <div className=\"space-y-1\">\n        {label && (\n          <label className=\"block text-sm font-medium text-secondary-700\">\n            {label}\n          </label>\n        )}\n        <div className=\"relative\">\n          {icon && (\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <div className=\"h-5 w-5 text-secondary-400\">\n                {icon}\n              </div>\n            </div>\n          )}\n          <input\n            type={type}\n            className={cn(\n              'block w-full rounded-lg border border-secondary-300 bg-white px-3 py-2 text-secondary-900 placeholder-secondary-500 focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500 disabled:cursor-not-allowed disabled:bg-secondary-50 disabled:text-secondary-500',\n              icon && 'pl-10',\n              error && 'border-red-500 focus:border-red-500 focus:ring-red-500',\n              className\n            )}\n            ref={ref}\n            {...props}\n          />\n        </div>\n        {error && (\n          <p className=\"text-sm text-red-600\">{error}</p>\n        )}\n      </div>\n    );\n  }\n);\n\nInput.displayName = 'Input';\n\nexport default Input;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,sBAAQ,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC5B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAClD,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,WAAU;0BACd;;;;;;0BAGL,8OAAC;gBAAI,WAAU;;oBACZ,sBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;kCAIP,8OAAC;wBACC,MAAM;wBACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wRACA,QAAQ,SACR,SAAS,0DACT;wBAEF,KAAK;wBACJ,GAAG,KAAK;;;;;;;;;;;;YAGZ,uBACC,8OAAC;gBAAE,WAAU;0BAAwB;;;;;;;;;;;;AAI7C;AAGF,MAAM,WAAW,GAAG;uCAEL"}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 475, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/laragon/www/nextjs/frontend/src/components/layout/Navbar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { useRouter } from 'next/navigation';\nimport { useAuthStore } from '@/store/authStore';\nimport { authAPI } from '@/lib/api';\nimport Button from '@/components/ui/Button';\nimport { Plane, Menu, X, User, LogOut } from 'lucide-react';\n\nconst Navbar = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);\n  const { user, isAuthenticated, logout } = useAuthStore();\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    try {\n      await authAPI.logout();\n      logout();\n      router.push('/');\n    } catch (error) {\n      console.error('Logout error:', error);\n      logout(); // Force logout even if API call fails\n      router.push('/');\n    }\n  };\n\n  return (\n    <nav className=\"bg-white/95 backdrop-blur-sm border-b border-secondary-200 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"bg-primary-600 p-2 rounded-lg\">\n              <Plane className=\"h-6 w-6 text-white\" />\n            </div>\n            <span className=\"text-xl font-bold text-secondary-900\">FlightBooker</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            <Link href=\"/flights\" className=\"text-secondary-700 hover:text-primary-600 transition-colors\">\n              Flights\n            </Link>\n            <Link href=\"/bookings\" className=\"text-secondary-700 hover:text-primary-600 transition-colors\">\n              My Bookings\n            </Link>\n            \n            {isAuthenticated ? (\n              <div className=\"relative\">\n                <button\n                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}\n                  className=\"flex items-center space-x-2 text-secondary-700 hover:text-primary-600 transition-colors\"\n                >\n                  <User className=\"h-5 w-5\" />\n                  <span>{user?.name}</span>\n                </button>\n                \n                {isUserMenuOpen && (\n                  <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-secondary-200 py-1\">\n                    <Link\n                      href=\"/profile\"\n                      className=\"block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50\"\n                      onClick={() => setIsUserMenuOpen(false)}\n                    >\n                      Profile\n                    </Link>\n                    <Link\n                      href=\"/bookings\"\n                      className=\"block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50\"\n                      onClick={() => setIsUserMenuOpen(false)}\n                    >\n                      My Bookings\n                    </Link>\n                    <button\n                      onClick={handleLogout}\n                      className=\"w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2\"\n                    >\n                      <LogOut className=\"h-4 w-4\" />\n                      <span>Logout</span>\n                    </button>\n                  </div>\n                )}\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-4\">\n                <Link href=\"/login\">\n                  <Button variant=\"ghost\">Login</Button>\n                </Link>\n                <Link href=\"/register\">\n                  <Button>Sign Up</Button>\n                </Link>\n              </div>\n            )}\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"text-secondary-700 hover:text-primary-600 transition-colors\"\n            >\n              {isMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden py-4 border-t border-secondary-200\">\n            <div className=\"flex flex-col space-y-4\">\n              <Link\n                href=\"/flights\"\n                className=\"text-secondary-700 hover:text-primary-600 transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Flights\n              </Link>\n              <Link\n                href=\"/bookings\"\n                className=\"text-secondary-700 hover:text-primary-600 transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                My Bookings\n              </Link>\n              \n              {isAuthenticated ? (\n                <div className=\"flex flex-col space-y-2 pt-4 border-t border-secondary-200\">\n                  <span className=\"text-sm text-secondary-500\">Welcome, {user?.name}</span>\n                  <Link\n                    href=\"/profile\"\n                    className=\"text-secondary-700 hover:text-primary-600 transition-colors\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    Profile\n                  </Link>\n                  <button\n                    onClick={handleLogout}\n                    className=\"text-left text-red-600 hover:text-red-700 transition-colors\"\n                  >\n                    Logout\n                  </button>\n                </div>\n              ) : (\n                <div className=\"flex flex-col space-y-2 pt-4 border-t border-secondary-200\">\n                  <Link href=\"/login\" onClick={() => setIsMenuOpen(false)}>\n                    <Button variant=\"ghost\" className=\"w-full justify-start\">Login</Button>\n                  </Link>\n                  <Link href=\"/register\" onClick={() => setIsMenuOpen(false)}>\n                    <Button className=\"w-full\">Sign Up</Button>\n                  </Link>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AAUA,MAAM,SAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IACrD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,iHAAA,CAAA,UAAO,CAAC,MAAM;YACpB;YACA,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,UAAU,sCAAsC;YAChD,OAAO,IAAI,CAAC;QACd;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,8OAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;sCAIzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAA8D;;;;;;8CAG9F,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAA8D;;;;;;gCAI9F,gCACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,kBAAkB,CAAC;4CAClC,WAAU;;8DAEV,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAM,MAAM;;;;;;;;;;;;wCAGd,gCACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,kBAAkB;8DAClC;;;;;;8DAGD,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,kBAAkB;8DAClC;;;;;;8DAGD,8OAAC;oDACC,SAAS;oDACT,WAAU;;sEAEV,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;yDAMd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC,kIAAA,CAAA,UAAM;gDAAC,SAAQ;0DAAQ;;;;;;;;;;;sDAE1B,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC,kIAAA,CAAA,UAAM;0DAAC;;;;;;;;;;;;;;;;;;;;;;;sCAOhB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;0CAET,2BAAa,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAAe,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAM/D,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;4BAIA,gCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CAA6B;4CAAU,MAAM;;;;;;;kDAC7D,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc;kDAC9B;;;;;;kDAGD,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;qDAKH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,SAAS,IAAM,cAAc;kDAC/C,cAAA,8OAAC,kIAAA,CAAA,UAAM;4CAAC,SAAQ;4CAAQ,WAAU;sDAAuB;;;;;;;;;;;kDAE3D,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,SAAS,IAAM,cAAc;kDAClD,cAAA,8OAAC,kIAAA,CAAA,UAAM;4CAAC,WAAU;sDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU/C;uCAEe"}}, {"offset": {"line": 868, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 874, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/laragon/www/nextjs/frontend/src/app/register/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport <PERSON> from 'next/link';\nimport { useRouter } from 'next/navigation';\nimport { useAuthStore } from '@/store/authStore';\nimport { authAPI } from '@/lib/api';\nimport Button from '@/components/ui/Button';\nimport Input from '@/components/ui/Input';\nimport Navbar from '@/components/layout/Navbar';\nimport { Mail, Lock, User, Plane } from 'lucide-react';\n\nconst RegisterPage = () => {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    password_confirmation: '',\n  });\n  const [errors, setErrors] = useState<Record<string, string>>({});\n  const [isLoading, setIsLoading] = useState(false);\n  \n  const { setAuth } = useAuthStore();\n  const router = useRouter();\n\n  const handleInputChange = (field: string, value: string) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: '' }));\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsLoading(true);\n    setErrors({});\n\n    // Client-side validation\n    if (formData.password !== formData.password_confirmation) {\n      setErrors({ password_confirmation: 'Passwords do not match' });\n      setIsLoading(false);\n      return;\n    }\n\n    try {\n      const response = await authAPI.register(formData);\n      setAuth(response.user, response.token);\n      router.push('/');\n    } catch (error: any) {\n      console.error('Registration error:', error);\n      if (error.response?.data?.errors) {\n        setErrors(error.response.data.errors);\n      } else {\n        setErrors({ general: error.response?.data?.message || 'Registration failed. Please try again.' });\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50\">\n      <Navbar />\n      \n      <div className=\"flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-md w-full space-y-8\">\n          <div className=\"text-center\">\n            <div className=\"bg-primary-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <Plane className=\"h-8 w-8 text-white\" />\n            </div>\n            <h2 className=\"text-3xl font-bold text-secondary-900\">Create your account</h2>\n            <p className=\"mt-2 text-secondary-600\">Join thousands of travelers worldwide</p>\n          </div>\n\n          <div className=\"bg-white rounded-2xl shadow-xl p-8\">\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              {errors.general && (\n                <div className=\"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg\">\n                  {errors.general}\n                </div>\n              )}\n\n              <Input\n                label=\"Full Name\"\n                type=\"text\"\n                placeholder=\"Enter your full name\"\n                value={formData.name}\n                onChange={(e) => handleInputChange('name', e.target.value)}\n                error={errors.name}\n                icon={<User />}\n                required\n              />\n\n              <Input\n                label=\"Email Address\"\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                value={formData.email}\n                onChange={(e) => handleInputChange('email', e.target.value)}\n                error={errors.email}\n                icon={<Mail />}\n                required\n              />\n\n              <Input\n                label=\"Password\"\n                type=\"password\"\n                placeholder=\"Create a password\"\n                value={formData.password}\n                onChange={(e) => handleInputChange('password', e.target.value)}\n                error={errors.password}\n                icon={<Lock />}\n                required\n              />\n\n              <Input\n                label=\"Confirm Password\"\n                type=\"password\"\n                placeholder=\"Confirm your password\"\n                value={formData.password_confirmation}\n                onChange={(e) => handleInputChange('password_confirmation', e.target.value)}\n                error={errors.password_confirmation}\n                icon={<Lock />}\n                required\n              />\n\n              <div className=\"flex items-start\">\n                <input\n                  type=\"checkbox\"\n                  className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded mt-1\"\n                  required\n                />\n                <span className=\"ml-2 text-sm text-secondary-700\">\n                  I agree to the{' '}\n                  <Link href=\"/terms\" className=\"text-primary-600 hover:text-primary-700\">\n                    Terms of Service\n                  </Link>{' '}\n                  and{' '}\n                  <Link href=\"/privacy\" className=\"text-primary-600 hover:text-primary-700\">\n                    Privacy Policy\n                  </Link>\n                </span>\n              </div>\n\n              <Button\n                type=\"submit\"\n                size=\"lg\"\n                className=\"w-full\"\n                isLoading={isLoading}\n              >\n                Create Account\n              </Button>\n            </form>\n\n            <div className=\"mt-6 text-center\">\n              <p className=\"text-secondary-600\">\n                Already have an account?{' '}\n                <Link href=\"/login\" className=\"text-primary-600 hover:text-primary-700 font-medium\">\n                  Sign in here\n                </Link>\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RegisterPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAVA;;;;;;;;;;;AAYA,MAAM,eAAe;IACnB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,UAAU;QACV,uBAAuB;IACzB;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,oBAAoB,CAAC,OAAe;QACxC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAChD,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QAC7C;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QACb,UAAU,CAAC;QAEX,yBAAyB;QACzB,IAAI,SAAS,QAAQ,KAAK,SAAS,qBAAqB,EAAE;YACxD,UAAU;gBAAE,uBAAuB;YAAyB;YAC5D,aAAa;YACb;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,QAAQ,CAAC;YACxC,QAAQ,SAAS,IAAI,EAAE,SAAS,KAAK;YACrC,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,uBAAuB;YACrC,IAAI,MAAM,QAAQ,EAAE,MAAM,QAAQ;gBAChC,UAAU,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM;YACtC,OAAO;gBACL,UAAU;oBAAE,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW;gBAAyC;YACjG;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,UAAM;;;;;0BAEP,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAA0B;;;;;;;;;;;;sCAGzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,UAAU;oCAAc,WAAU;;wCACrC,OAAO,OAAO,kBACb,8OAAC;4CAAI,WAAU;sDACZ,OAAO,OAAO;;;;;;sDAInB,8OAAC,iIAAA,CAAA,UAAK;4CACJ,OAAM;4CACN,MAAK;4CACL,aAAY;4CACZ,OAAO,SAAS,IAAI;4CACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;4CACzD,OAAO,OAAO,IAAI;4CAClB,oBAAM,8OAAC,kMAAA,CAAA,OAAI;;;;;4CACX,QAAQ;;;;;;sDAGV,8OAAC,iIAAA,CAAA,UAAK;4CACJ,OAAM;4CACN,MAAK;4CACL,aAAY;4CACZ,OAAO,SAAS,KAAK;4CACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;4CAC1D,OAAO,OAAO,KAAK;4CACnB,oBAAM,8OAAC,kMAAA,CAAA,OAAI;;;;;4CACX,QAAQ;;;;;;sDAGV,8OAAC,iIAAA,CAAA,UAAK;4CACJ,OAAM;4CACN,MAAK;4CACL,aAAY;4CACZ,OAAO,SAAS,QAAQ;4CACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC7D,OAAO,OAAO,QAAQ;4CACtB,oBAAM,8OAAC,kMAAA,CAAA,OAAI;;;;;4CACX,QAAQ;;;;;;sDAGV,8OAAC,iIAAA,CAAA,UAAK;4CACJ,OAAM;4CACN,MAAK;4CACL,aAAY;4CACZ,OAAO,SAAS,qBAAqB;4CACrC,UAAU,CAAC,IAAM,kBAAkB,yBAAyB,EAAE,MAAM,CAAC,KAAK;4CAC1E,OAAO,OAAO,qBAAqB;4CACnC,oBAAM,8OAAC,kMAAA,CAAA,OAAI;;;;;4CACX,QAAQ;;;;;;sDAGV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,WAAU;oDACV,QAAQ;;;;;;8DAEV,8OAAC;oDAAK,WAAU;;wDAAkC;wDACjC;sEACf,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAS,WAAU;sEAA0C;;;;;;wDAEhE;wDAAI;wDACR;sEACJ,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAW,WAAU;sEAA0C;;;;;;;;;;;;;;;;;;sDAM9E,8OAAC,kIAAA,CAAA,UAAM;4CACL,MAAK;4CACL,MAAK;4CACL,WAAU;4CACV,WAAW;sDACZ;;;;;;;;;;;;8CAKH,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;4CAAqB;4CACP;0DACzB,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAsD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpG;uCAEe"}}, {"offset": {"line": 1211, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}