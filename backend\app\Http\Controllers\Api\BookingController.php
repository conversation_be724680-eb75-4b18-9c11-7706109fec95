<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\BookingResource;
use App\Models\Booking;
use App\Models\Flight;
use Illuminate\Http\Request;
use Carbon\Carbon;

class BookingController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum');
    }

    public function store(Request $request)
    {
        $request->validate([
            'flight_id' => 'required|exists:flights,id',
            'passenger_name' => 'required|string|max:255',
            'passenger_email' => 'required|email|max:255',
            'passenger_phone' => 'required|string|max:20',
            'passenger_dob' => 'required|date|before:today',
            'passenger_passport' => 'nullable|string|max:20',
            'class' => 'required|in:economy,business,first',
            'passengers_count' => 'integer|min:1|max:9',
            'passenger_details' => 'nullable|array',
        ]);

        $flight = Flight::findOrFail($request->flight_id);

        // Check seat availability
        $availableSeats = $flight->getAvailableSeatsForClass($request->class);
        if ($availableSeats < $request->passengers_count) {
            return response()->json([
                'message' => 'Not enough seats available for the selected class.'
            ], 400);
        }

        // Calculate total price
        $pricePerSeat = $flight->getPriceForClass($request->class);
        $totalPrice = $pricePerSeat * $request->passengers_count;

        $booking = Booking::create([
            'user_id' => $request->user()->id,
            'flight_id' => $request->flight_id,
            'booking_reference' => Booking::generateBookingReference(),
            'passenger_name' => $request->passenger_name,
            'passenger_email' => $request->passenger_email,
            'passenger_phone' => $request->passenger_phone,
            'passenger_dob' => $request->passenger_dob,
            'passenger_passport' => $request->passenger_passport,
            'class' => $request->class,
            'passengers_count' => $request->passengers_count ?? 1,
            'total_price' => $totalPrice,
            'status' => 'confirmed',
            'passenger_details' => $request->passenger_details,
            'booking_date' => Carbon::now(),
        ]);

        // Update available seats
        $seatColumn = 'available_seats_' . $request->class;
        $flight->decrement($seatColumn, $request->passengers_count ?? 1);

        return new BookingResource($booking->load('flight'));
    }

    public function index(Request $request)
    {
        $bookings = $request->user()->bookings()->with('flight')->latest()->get();
        return BookingResource::collection($bookings);
    }

    public function show(Request $request, Booking $booking)
    {
        // Ensure user can only see their own bookings
        if ($booking->user_id !== $request->user()->id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        return new BookingResource($booking->load('flight'));
    }

    public function cancel(Request $request, Booking $booking)
    {
        // Ensure user can only cancel their own bookings
        if ($booking->user_id !== $request->user()->id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        if ($booking->status === 'cancelled') {
            return response()->json(['message' => 'Booking is already cancelled'], 400);
        }

        $booking->update(['status' => 'cancelled']);

        // Restore available seats
        $flight = $booking->flight;
        $seatColumn = 'available_seats_' . $booking->class;
        $flight->increment($seatColumn, $booking->passengers_count);

        return new BookingResource($booking->load('flight'));
    }
}
