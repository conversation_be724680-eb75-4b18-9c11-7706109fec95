'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { Flight } from '@/store/flightStore';
import { formatCurrency } from '@/lib/utils';
import Button from '@/components/ui/Button';
import { Plane, Clock, MapPin, Wifi, Coffee, Tv } from 'lucide-react';

interface FlightCardProps {
  flight: Flight;
  selectedClass: 'economy' | 'business' | 'first';
}

const FlightCard: React.FC<FlightCardProps> = ({ flight, selectedClass }) => {
  const router = useRouter();

  const handleBookFlight = () => {
    router.push(`/flights/${flight.id}/book?class=${selectedClass}`);
  };

  const getPrice = () => {
    return flight.prices[selectedClass] || flight.prices.economy;
  };

  const getAvailableSeats = () => {
    return flight.available_seats[selectedClass] || flight.available_seats.economy;
  };

  const getAmenityIcon = (amenity: string) => {
    switch (amenity.toLowerCase()) {
      case 'wifi':
        return <Wifi className="h-4 w-4" />;
      case 'entertainment':
        return <Tv className="h-4 w-4" />;
      case 'meals':
        return <Coffee className="h-4 w-4" />;
      default:
        return null;
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-lg border border-secondary-200 p-6 hover:shadow-xl transition-shadow">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
        {/* Flight Info */}
        <div className="flex-1">
          <div className="flex items-center gap-4 mb-4">
            {flight.airline_logo_url && (
              <img
                src={flight.airline_logo_url}
                alt={flight.airline_name}
                className="h-8 w-8 object-contain"
              />
            )}
            <div>
              <h3 className="font-semibold text-secondary-900">
                {flight.airline_name} {flight.flight_number}
              </h3>
              <p className="text-sm text-secondary-600">{flight.aircraft_type}</p>
            </div>
          </div>

          {/* Route */}
          <div className="flex items-center gap-4 mb-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-secondary-900">
                {flight.departure.formatted_time}
              </p>
              <p className="text-sm text-secondary-600">{flight.departure.airport}</p>
              <p className="text-xs text-secondary-500">{flight.departure.city}</p>
            </div>

            <div className="flex-1 flex items-center justify-center">
              <div className="flex items-center gap-2 text-secondary-500">
                <div className="h-px bg-secondary-300 flex-1"></div>
                <div className="text-center">
                  <Plane className="h-4 w-4 mx-auto mb-1" />
                  <p className="text-xs">{flight.duration_formatted}</p>
                  {flight.stops > 0 && (
                    <p className="text-xs text-orange-600">
                      {flight.stops} stop{flight.stops > 1 ? 's' : ''}
                    </p>
                  )}
                </div>
                <div className="h-px bg-secondary-300 flex-1"></div>
              </div>
            </div>

            <div className="text-center">
              <p className="text-2xl font-bold text-secondary-900">
                {flight.arrival.formatted_time}
              </p>
              <p className="text-sm text-secondary-600">{flight.arrival.airport}</p>
              <p className="text-xs text-secondary-500">{flight.arrival.city}</p>
            </div>
          </div>

          {/* Amenities */}
          {flight.amenities && flight.amenities.length > 0 && (
            <div className="flex items-center gap-3 text-secondary-500">
              {flight.amenities.slice(0, 4).map((amenity, index) => (
                <div key={index} className="flex items-center gap-1">
                  {getAmenityIcon(amenity)}
                  <span className="text-xs">{amenity}</span>
                </div>
              ))}
              {flight.amenities.length > 4 && (
                <span className="text-xs">+{flight.amenities.length - 4} more</span>
              )}
            </div>
          )}
        </div>

        {/* Price and Booking */}
        <div className="lg:text-right">
          <div className="mb-4">
            <p className="text-3xl font-bold text-primary-600">
              {formatCurrency(getPrice())}
            </p>
            <p className="text-sm text-secondary-600">per person</p>
            <p className="text-xs text-secondary-500 capitalize">
              {selectedClass} class
            </p>
          </div>

          <div className="mb-4">
            <p className="text-sm text-secondary-600">
              {getAvailableSeats()} seats left
            </p>
            {flight.is_refundable && (
              <p className="text-xs text-green-600">✓ Refundable</p>
            )}
          </div>

          <Button
            onClick={handleBookFlight}
            size="lg"
            className="w-full lg:w-auto"
          >
            Book Flight
          </Button>
        </div>
      </div>
    </div>
  );
};

export default FlightCard;
