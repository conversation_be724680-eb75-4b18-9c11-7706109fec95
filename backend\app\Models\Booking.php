<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Booking extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'flight_id',
        'booking_reference',
        'passenger_name',
        'passenger_email',
        'passenger_phone',
        'passenger_dob',
        'passenger_passport',
        'class',
        'passengers_count',
        'total_price',
        'status',
        'passenger_details',
        'booking_date',
    ];

    protected $casts = [
        'passenger_dob' => 'date',
        'passenger_details' => 'array',
        'total_price' => 'decimal:2',
        'booking_date' => 'datetime',
    ];

    /**
     * Get the user that owns the booking.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the flight that belongs to the booking.
     */
    public function flight()
    {
        return $this->belongsTo(Flight::class);
    }

    /**
     * Generate a unique booking reference.
     */
    public static function generateBookingReference()
    {
        do {
            $reference = 'BK' . strtoupper(substr(md5(uniqid()), 0, 6));
        } while (self::where('booking_reference', $reference)->exists());

        return $reference;
    }
}
