<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FlightResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'airline_name' => $this->airline_name,
            'airline_code' => $this->airline_code,
            'flight_number' => $this->flight_number,
            'aircraft_type' => $this->aircraft_type,
            'departure' => [
                'airport' => $this->departure_airport,
                'city' => $this->departure_city,
                'country' => $this->departure_country,
                'time' => $this->departure_time->format('Y-m-d H:i:s'),
                'formatted_time' => $this->departure_time->format('H:i'),
                'formatted_date' => $this->departure_time->format('M d, Y'),
            ],
            'arrival' => [
                'airport' => $this->arrival_airport,
                'city' => $this->arrival_city,
                'country' => $this->arrival_country,
                'time' => $this->arrival_time->format('Y-m-d H:i:s'),
                'formatted_time' => $this->arrival_time->format('H:i'),
                'formatted_date' => $this->arrival_time->format('M d, Y'),
            ],
            'duration_minutes' => $this->duration_minutes,
            'duration_formatted' => $this->formatDuration($this->duration_minutes),
            'stops' => $this->stops,
            'stop_airports' => $this->stop_airports,
            'prices' => [
                'economy' => $this->price_economy,
                'business' => $this->price_business,
                'first' => $this->price_first,
            ],
            'available_seats' => [
                'economy' => $this->available_seats_economy,
                'business' => $this->available_seats_business,
                'first' => $this->available_seats_first,
            ],
            'airline_logo_url' => $this->airline_logo_url,
            'amenities' => $this->amenities,
            'baggage_policy' => $this->baggage_policy,
            'cancellation_policy' => $this->cancellation_policy,
            'is_refundable' => $this->is_refundable,
        ];
    }

    private function formatDuration($minutes)
    {
        $hours = floor($minutes / 60);
        $mins = $minutes % 60;
        return $hours . 'h ' . $mins . 'm';
    }
}
